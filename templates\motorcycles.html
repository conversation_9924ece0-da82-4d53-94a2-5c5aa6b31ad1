{% extends "base.html" %}

{% block title %}إدارة الدراجات النارية - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-motorcycle text-primary"></i>
                إدارة الدراجات النارية
            </h1>
            <a href="{{ url_for('add_motorcycle') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة دراجة جديدة
            </a>
        </div>
    </div>
</div>

<!-- جدول الدراجات النارية -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i>
                    قائمة الدراجات النارية ({{ motorcycles|length }} دراجة)
                </h5>
            </div>
            <div class="card-body">
                {% if motorcycles %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الماركة</th>
                                <th>الموديل</th>
                                <th>سنة الصنع</th>
                                <th>حجم المحرك</th>
                                <th>اللون</th>
                                <th>عدد المبيعات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for motorcycle in motorcycles %}
                            <tr>
                                <td>{{ motorcycle.id }}</td>
                                <td>
                                    <strong>{{ motorcycle.brand }}</strong>
                                </td>
                                <td>{{ motorcycle.model }}</td>
                                <td>{{ motorcycle.year }}</td>
                                <td>
                                    {% if motorcycle.engine_size %}
                                    <span class="badge bg-info">{{ motorcycle.engine_size }}</span>
                                    {% else %}
                                    -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if motorcycle.color %}
                                    <span class="badge bg-secondary">{{ motorcycle.color }}</span>
                                    {% else %}
                                    -
                                    {% endif %}
                                </td>

                                <td>
                                    <span class="badge bg-primary">{{ motorcycle.invoices|length }}</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('edit_motorcycle', id=motorcycle.id) }}"
                                           class="btn btn-sm btn-outline-primary"
                                           data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#"
                                           class="btn btn-sm btn-outline-info"
                                           data-bs-toggle="tooltip" title="تفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('delete_motorcycle', id=motorcycle.id) }}"
                                           class="btn btn-sm btn-outline-danger"
                                           data-bs-toggle="tooltip" title="حذف"
                                           onclick="return confirmDelete('هل أنت متأكد من حذف هذه الدراجة؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-motorcycle fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد دراجات نارية</h4>
                    <p class="text-muted">ابدأ بإضافة دراجة نارية جديدة</p>
                    <a href="{{ url_for('add_motorcycle') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        إضافة دراجة جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
{% if motorcycles %}
<div class="row mt-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4>{{ motorcycles|length }}</h4>
                <p class="mb-0">إجمالي الدراجات</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4>{{ motorcycles|sum(attribute='invoices')|length }}</h4>
                <p class="mb-0">إجمالي المبيعات</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h4>{{ motorcycles|groupby('brand')|list|length }}</h4>
                <p class="mb-0">عدد الماركات</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4>{{ motorcycles|groupby('color')|list|length }}</h4>
                <p class="mb-0">عدد الألوان</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- أزرار إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex gap-2 flex-wrap">
            <button class="btn btn-outline-success" onclick="exportToCSV('motorcyclesTable', 'motorcycles.csv')">
                <i class="fas fa-file-excel"></i>
                تصدير Excel
            </button>
            <button class="btn btn-outline-info btn-print">
                <i class="fas fa-print"></i>
                طباعة
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تهيئة التلميحات
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
