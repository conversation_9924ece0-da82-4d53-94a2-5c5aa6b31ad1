{% extends "base.html" %}

{% block title %}تعديل بيانات العميل - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-user-edit text-primary"></i>
                تعديل بيانات العميل
            </h1>
            <a href="{{ url_for('customers') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right"></i>
                العودة للعملاء
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user"></i>
                    تعديل بيانات: {{ customer.name }}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- الاسم الكامل -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label required">الاسم الكامل</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ customer.name }}" required>
                            <div class="invalid-feedback">
                                يرجى إدخال الاسم الكامل
                            </div>
                        </div>
                        
                        <!-- رقم الهاتف -->
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label required">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="{{ customer.phone }}" placeholder="05xxxxxxxx" required>
                            <div class="invalid-feedback">
                                يرجى إدخال رقم هاتف صحيح
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- رقم الهوية -->
                        <div class="col-md-6 mb-3">
                            <label for="national_id" class="form-label">رقم الهوية الوطنية</label>
                            <input type="text" class="form-control" id="national_id" name="national_id" 
                                   value="{{ customer.national_id or '' }}" placeholder="1234567890" maxlength="10">
                            <div class="form-text">اختياري - 10 أرقام</div>
                        </div>
                        
                        <!-- البريد الإلكتروني -->
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ customer.email or '' }}" placeholder="<EMAIL>">
                            <div class="form-text">اختياري</div>
                        </div>
                    </div>
                    
                    <!-- العنوان -->
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3" 
                                  placeholder="المدينة - الحي - الشارع">{{ customer.address or '' }}</textarea>
                        <div class="form-text">اختياري</div>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="d-flex gap-2 justify-content-end">
                        <a href="{{ url_for('customers') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ التعديلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات العميل -->
<div class="row justify-content-center mt-4">
    <div class="col-lg-8">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i>
                    معلومات العميل
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>تاريخ التسجيل:</strong> {{ customer.created_at.strftime('%Y-%m-%d') }}</p>
                        <p><strong>رقم العميل:</strong> {{ customer.id }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>إجمالي الديون:</strong> 
                            {% set debt = customer.get_total_debt() %}
                            {% if debt > 0 %}
                                <span class="text-warning">{{ "{:,.0f}".format(debt) }} ر.س</span>
                            {% else %}
                                <span class="text-success">لا توجد ديون</span>
                            {% endif %}
                        </p>
                        <p><strong>عدد الفواتير:</strong> {{ customer.invoices|length }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من رقم الهاتف
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, ''); // إزالة كل شيء عدا الأرقام
        
        if (value.length > 0 && !value.startsWith('05')) {
            if (value.startsWith('5')) {
                value = '0' + value;
            } else if (!value.startsWith('0')) {
                value = '05' + value;
            }
        }
        
        if (value.length > 10) {
            value = value.substring(0, 10);
        }
        
        this.value = value;
    });
    
    // التحقق من رقم الهوية
    const nationalIdInput = document.getElementById('national_id');
    nationalIdInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, ''); // إزالة كل شيء عدا الأرقام
        
        if (value.length > 10) {
            value = value.substring(0, 10);
        }
        
        this.value = value;
    });
    
    // تنسيق الاسم
    const nameInput = document.getElementById('name');
    nameInput.addEventListener('blur', function() {
        // تنظيف المسافات الزائدة
        this.value = this.value.trim().replace(/\s+/g, ' ');
    });
});
</script>
{% endblock %}
