{% extends "base.html" %}

{% block title %}إدارة العملات - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-coins text-primary"></i>
                إدارة العملات
            </h1>
            <a href="{{ url_for('add_currency') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة عملة جديدة
            </a>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ currencies|length }}</h4>
                        <p class="mb-0">إجمالي العملات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-coins fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ currencies|selectattr('is_active')|list|length }}</h4>
                        <p class="mb-0">عملات نشطة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ currencies|selectattr('is_base')|list|length }}</h4>
                        <p class="mb-0">العملة الأساسية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-star fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ currencies|rejectattr('is_active')|list|length }}</h4>
                        <p class="mb-0">عملات معطلة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-pause-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول العملات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i>
                    قائمة العملات
                </h5>
            </div>
            <div class="card-body">
                {% if currencies %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم العملة</th>
                                <th>الرمز</th>
                                <th>الرمز المختصر</th>
                                <th>سعر الصرف</th>
                                <th>النوع</th>
                                <th>الحالة</th>
                                <th>تاريخ الإضافة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for currency in currencies %}
                            <tr class="{% if not currency.is_active %}table-secondary{% elif currency.is_base %}table-warning{% endif %}">
                                <td>
                                    <strong>{{ currency.name }}</strong>
                                    {% if currency.is_base %}
                                    <span class="badge bg-warning ms-2">أساسية</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ currency.symbol }}</span>
                                </td>
                                <td>
                                    <code>{{ currency.code }}</code>
                                </td>
                                <td>
                                    {% if currency.is_base %}
                                    <span class="text-muted">1.00 (أساسية)</span>
                                    {% else %}
                                    {{ "{:.2f}".format(currency.exchange_rate) }}
                                    {% endif %}
                                </td>
                                <td>
                                    {% if currency.is_base %}
                                    <span class="badge bg-warning">عملة أساسية</span>
                                    {% else %}
                                    <span class="badge bg-info">عملة أجنبية</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if currency.is_active %}
                                    <span class="badge bg-success">نشطة</span>
                                    {% else %}
                                    <span class="badge bg-secondary">معطلة</span>
                                    {% endif %}
                                </td>
                                <td>{{ currency.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('edit_currency', id=currency.id) }}"
                                           class="btn btn-sm btn-warning"
                                           data-bs-toggle="tooltip" title="تعديل العملة">
                                            <i class="fas fa-edit"></i>
                                            تعديل
                                        </a>
                                        {% if not currency.is_base %}
                                        <a href="{{ url_for('delete_currency', id=currency.id) }}"
                                           class="btn btn-sm btn-danger"
                                           data-bs-toggle="tooltip" title="حذف العملة"
                                           onclick="return confirmDelete('هل أنت متأكد من حذف عملة {{ currency.name }}؟\n\nتأكد من عدم وجود معاملات مرتبطة بهذه العملة.')">
                                            <i class="fas fa-trash"></i>
                                            حذف
                                        </a>
                                        {% else %}
                                        <span class="btn btn-sm btn-secondary disabled"
                                              data-bs-toggle="tooltip" title="لا يمكن حذف العملة الأساسية">
                                            <i class="fas fa-lock"></i>
                                            محمية
                                        </span>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-coins fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد عملات مضافة</h5>
                    <p class="text-muted">ابدأ بإضافة عملة جديدة</p>
                    <a href="{{ url_for('add_currency') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        إضافة عملة جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i>
                    معلومات مهمة حول العملات
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-star text-warning"></i> العملة الأساسية:</h6>
                        <ul class="list-unstyled">
                            <li>• يجب أن تكون هناك عملة أساسية واحدة فقط</li>
                            <li>• سعر صرف العملة الأساسية دائماً 1.00</li>
                            <li>• لا يمكن حذف العملة الأساسية</li>
                            <li>• جميع العملات الأخرى تُحسب مقابلها</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-exchange-alt text-info"></i> أسعار الصرف:</h6>
                        <ul class="list-unstyled">
                            <li>• يتم تحديث أسعار الصرف يدوياً</li>
                            <li>• السعر يمثل كم من العملة الأساسية يساوي وحدة واحدة من هذه العملة</li>
                            <li>• مثال: إذا كان الدولار = 3.75 فهذا يعني دولار واحد = 3.75 ريال</li>
                            <li>• يمكن تعطيل العملات غير المستخدمة</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تهيئة التلميحات
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

function confirmDelete(message) {
    return confirm(message);
}
</script>
{% endblock %}
