<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام محاسبة مبيعات الدراجات النارية{% endblock %}</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">

    <!-- PWA Manifest -->
    <link rel="manifest" href="{{ url_for('static', filename='manifest.json') }}">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#0d6efd">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="محاسبة الدراجات">

    <!-- PWA Icons -->
    <link rel="apple-touch-icon" href="{{ url_for('static', filename='images/icon-192.png') }}">
    <link rel="icon" type="image/png" sizes="192x192" href="{{ url_for('static', filename='images/icon-192.png') }}">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-motorcycle me-2"></i>
                نظام محاسبة الدراجات النارية
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="customersDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-users"></i> العملاء
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="customersDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('add_customer') }}">
                                <i class="fas fa-plus"></i> إضافة عميل جديد
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('customers') }}">
                                <i class="fas fa-list"></i> عرض جميع العملاء
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="guarantorsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-shield"></i> الضامنين
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="guarantorsDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('add_guarantor') }}">
                                <i class="fas fa-plus"></i> إضافة ضامن جديد
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('guarantors') }}">
                                <i class="fas fa-list"></i> عرض جميع الضامنين
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="motorcycleDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-motorcycle"></i> الدراجات
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="motorcycleDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('add_motorcycle') }}">
                                <i class="fas fa-plus"></i> إضافة دراجة جديدة
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('motorcycles') }}">
                                <i class="fas fa-list"></i> عرض جميع الدراجات
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="invoicesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-file-invoice"></i> الفواتير
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="invoicesDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('add_invoice') }}">
                                <i class="fas fa-plus"></i> إنشاء فاتورة جديدة
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('invoices') }}">
                                <i class="fas fa-list"></i> عرض جميع الفواتير
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('receipts') }}">
                            <i class="fas fa-receipt"></i> سندات القبض
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="currenciesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-coins"></i> العملات
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="currenciesDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('add_currency') }}">
                                <i class="fas fa-plus"></i> إضافة عملة جديدة
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('currencies') }}">
                                <i class="fas fa-list"></i> إدارة العملات
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('reports') }}">
                            <i class="fas fa-chart-bar"></i> التقارير
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main class="container-fluid mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0 text-muted">
                &copy; 2024 نظام محاسبة مبيعات الدراجات النارية - جميع الحقوق محفوظة
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- PWA Service Worker -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/static/sw.js')
                .then(function(registration) {
                    console.log('SW registered: ', registration);
                }, function(registrationError) {
                    console.log('SW registration failed: ', registrationError);
                });
            });
        }

        // PWA Install Prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;

            // إظهار زر التثبيت
            const installButton = document.createElement('button');
            installButton.className = 'btn btn-primary position-fixed';
            installButton.style.cssText = 'bottom: 20px; left: 20px; z-index: 1000;';
            installButton.innerHTML = '<i class="fas fa-download"></i> تثبيت التطبيق';
            installButton.onclick = () => {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                    }
                    deferredPrompt = null;
                    installButton.remove();
                });
            };

            document.body.appendChild(installButton);

            // إخفاء الزر بعد 10 ثوان
            setTimeout(() => {
                if (installButton.parentNode) {
                    installButton.remove();
                }
            }, 10000);
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
