{% extends "base.html" %}

{% block title %}تعديل بيانات الدراجة النارية - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-motorcycle text-primary"></i>
                تعديل بيانات الدراجة النارية
            </h1>
            <a href="{{ url_for('motorcycles') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right"></i>
                العودة للدراجات
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-motorcycle"></i>
                    تعديل بيانات: {{ motorcycle.brand }} {{ motorcycle.model }}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- الماركة -->
                        <div class="col-md-6 mb-3">
                            <label for="brand" class="form-label required">الماركة</label>
                            <select class="form-select" id="brand" name="brand" required>
                                <option value="">اختر الماركة</option>
                                <option value="هوندا" {% if motorcycle.brand == 'هوندا' %}selected{% endif %}>هوندا (Honda)</option>
                                <option value="ياماها" {% if motorcycle.brand == 'ياماها' %}selected{% endif %}>ياماها (Yamaha)</option>
                                <option value="سوزوكي" {% if motorcycle.brand == 'سوزوكي' %}selected{% endif %}>سوزوكي (Suzuki)</option>
                                <option value="كاواساكي" {% if motorcycle.brand == 'كاواساكي' %}selected{% endif %}>كاواساكي (Kawasaki)</option>
                                <option value="دوكاتي" {% if motorcycle.brand == 'دوكاتي' %}selected{% endif %}>دوكاتي (Ducati)</option>
                                <option value="بي إم دبليو" {% if motorcycle.brand == 'بي إم دبليو' %}selected{% endif %}>بي إم دبليو (BMW)</option>
                                <option value="هارلي ديفيدسون" {% if motorcycle.brand == 'هارلي ديفيدسون' %}selected{% endif %}>هارلي ديفيدسون (Harley-Davidson)</option>
                                <option value="{{ motorcycle.brand }}" {% if motorcycle.brand not in ['هوندا', 'ياماها', 'سوزوكي', 'كاواساكي', 'دوكاتي', 'بي إم دبليو', 'هارلي ديفيدسون'] %}selected{% endif %}>{{ motorcycle.brand }}</option>
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار الماركة
                            </div>
                        </div>
                        
                        <!-- الموديل -->
                        <div class="col-md-6 mb-3">
                            <label for="model" class="form-label required">الموديل</label>
                            <input type="text" class="form-control" id="model" name="model" 
                                   value="{{ motorcycle.model }}" placeholder="مثال: CBR 150" required>
                            <div class="invalid-feedback">
                                يرجى إدخال الموديل
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- سنة الصنع -->
                        <div class="col-md-4 mb-3">
                            <label for="year" class="form-label">سنة الصنع</label>
                            <select class="form-select" id="year" name="year">
                                <option value="">اختر السنة</option>
                                {% for year in range(2024, 2019, -1) %}
                                <option value="{{ year }}" {% if motorcycle.year == year %}selected{% endif %}>{{ year }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <!-- حجم المحرك -->
                        <div class="col-md-4 mb-3">
                            <label for="engine_size" class="form-label">حجم المحرك</label>
                            <select class="form-select" id="engine_size" name="engine_size">
                                <option value="">اختر حجم المحرك</option>
                                <option value="125cc" {% if motorcycle.engine_size == '125cc' %}selected{% endif %}>125cc</option>
                                <option value="150cc" {% if motorcycle.engine_size == '150cc' %}selected{% endif %}>150cc</option>
                                <option value="200cc" {% if motorcycle.engine_size == '200cc' %}selected{% endif %}>200cc</option>
                                <option value="250cc" {% if motorcycle.engine_size == '250cc' %}selected{% endif %}>250cc</option>
                                <option value="300cc" {% if motorcycle.engine_size == '300cc' %}selected{% endif %}>300cc</option>
                                <option value="400cc" {% if motorcycle.engine_size == '400cc' %}selected{% endif %}>400cc</option>
                                <option value="500cc" {% if motorcycle.engine_size == '500cc' %}selected{% endif %}>500cc</option>
                                <option value="600cc" {% if motorcycle.engine_size == '600cc' %}selected{% endif %}>600cc</option>
                                <option value="750cc" {% if motorcycle.engine_size == '750cc' %}selected{% endif %}>750cc</option>
                                <option value="1000cc" {% if motorcycle.engine_size == '1000cc' %}selected{% endif %}>1000cc</option>
                                {% if motorcycle.engine_size and motorcycle.engine_size not in ['125cc', '150cc', '200cc', '250cc', '300cc', '400cc', '500cc', '600cc', '750cc', '1000cc'] %}
                                <option value="{{ motorcycle.engine_size }}" selected>{{ motorcycle.engine_size }}</option>
                                {% endif %}
                            </select>
                        </div>
                        
                        <!-- اللون -->
                        <div class="col-md-4 mb-3">
                            <label for="color" class="form-label">اللون</label>
                            <select class="form-select" id="color" name="color">
                                <option value="">اختر اللون</option>
                                <option value="أحمر" {% if motorcycle.color == 'أحمر' %}selected{% endif %}>أحمر</option>
                                <option value="أزرق" {% if motorcycle.color == 'أزرق' %}selected{% endif %}>أزرق</option>
                                <option value="أسود" {% if motorcycle.color == 'أسود' %}selected{% endif %}>أسود</option>
                                <option value="أبيض" {% if motorcycle.color == 'أبيض' %}selected{% endif %}>أبيض</option>
                                <option value="أخضر" {% if motorcycle.color == 'أخضر' %}selected{% endif %}>أخضر</option>
                                <option value="أصفر" {% if motorcycle.color == 'أصفر' %}selected{% endif %}>أصفر</option>
                                <option value="فضي" {% if motorcycle.color == 'فضي' %}selected{% endif %}>فضي</option>
                                <option value="رمادي" {% if motorcycle.color == 'رمادي' %}selected{% endif %}>رمادي</option>
                                <option value="برتقالي" {% if motorcycle.color == 'برتقالي' %}selected{% endif %}>برتقالي</option>
                                <option value="بنفسجي" {% if motorcycle.color == 'بنفسجي' %}selected{% endif %}>بنفسجي</option>
                                {% if motorcycle.color and motorcycle.color not in ['أحمر', 'أزرق', 'أسود', 'أبيض', 'أخضر', 'أصفر', 'فضي', 'رمادي', 'برتقالي', 'بنفسجي'] %}
                                <option value="{{ motorcycle.color }}" selected>{{ motorcycle.color }}</option>
                                {% endif %}
                            </select>
                        </div>
                    </div>
                    
                    <!-- السعر والعملة -->
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="price" class="form-label required">السعر</label>
                            <input type="number" class="form-control" id="price" name="price"
                                   value="{{ motorcycle.price }}" placeholder="15000" min="1" step="0.01" required>
                            <div class="invalid-feedback">
                                يرجى إدخال السعر
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="currency_id" class="form-label required">العملة</label>
                            <select class="form-select" id="currency_id" name="currency_id" required>
                                {% for currency in currencies %}
                                <option value="{{ currency.id }}"
                                        {% if currency.id == motorcycle.currency_id %}selected{% endif %}>
                                    {{ currency.name }} ({{ currency.symbol }})
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار العملة
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="d-flex gap-2 justify-content-end">
                        <a href="{{ url_for('motorcycles') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ التعديلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات الدراجة -->
<div class="row justify-content-center mt-4">
    <div class="col-lg-8">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i>
                    معلومات الدراجة النارية
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>تاريخ الإضافة:</strong> {{ motorcycle.created_at.strftime('%Y-%m-%d') }}</p>
                        <p><strong>رقم الدراجة:</strong> {{ motorcycle.id }}</p>
                        <p><strong>عدد المبيعات:</strong> {{ motorcycle.invoices|length }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>السعر الحالي:</strong>
                            {{ "{:,.0f}".format(motorcycle.price) }}
                            {% if motorcycle.currency_ref %}
                            {{ motorcycle.currency_ref.symbol }}
                            {% else %}
                            ر.س
                            {% endif %}
                        </p>
                        <p><strong>إجمالي الإيرادات:</strong> 
                            {% set total_revenue = motorcycle.invoices|sum(attribute='total_amount') %}
                            {{ "{:,.0f}".format(total_revenue) }} ر.س
                        </p>
                    </div>
                </div>
                
                {% if motorcycle.invoices %}
                <hr>
                <h6><i class="fas fa-file-invoice"></i> الفواتير المرتبطة:</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in motorcycle.invoices %}
                            <tr>
                                <td>{{ invoice.invoice_number }}</td>
                                <td>{{ invoice.customer.name }}</td>
                                <td>{{ "{:,.0f}".format(invoice.total_amount) }} ر.س</td>
                                <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if invoice.status == 'نشط' %}
                                    <span class="badge bg-success">{{ invoice.status }}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ invoice.status }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تنسيق السعر
    const priceInput = document.getElementById('price');
    priceInput.addEventListener('input', function() {
        // إزالة الأحرف غير الرقمية عدا النقطة
        let value = this.value.replace(/[^0-9.]/g, '');
        
        // التأكد من وجود نقطة واحدة فقط
        const parts = value.split('.');
        if (parts.length > 2) {
            value = parts[0] + '.' + parts.slice(1).join('');
        }
        
        this.value = value;
    });
});
</script>
{% endblock %}
