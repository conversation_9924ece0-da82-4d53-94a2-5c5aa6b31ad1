{% extends "base.html" %}

{% block title %}إدارة العملاء - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-users text-primary"></i>
                إدارة العملاء
            </h1>
            <a href="{{ url_for('add_customer') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة عميل جديد
            </a>
        </div>
    </div>
</div>

<!-- شريط البحث -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-8">
                        <input type="text" class="form-control" name="search" 
                               placeholder="البحث بالاسم أو رقم الهاتف أو رقم الهوية..." 
                               value="{{ search }}">
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-outline-primary w-100">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول العملاء -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i>
                    قائمة العملاء ({{ customers.total }} عميل)
                </h5>
            </div>
            <div class="card-body">
                {% if customers.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الاسم</th>
                                <th>رقم الهاتف</th>
                                <th>رقم الهوية</th>
                                <th>البريد الإلكتروني</th>
                                <th>إجمالي الديون</th>
                                <th>تاريخ التسجيل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in customers.items %}
                            <tr>
                                <td>{{ customer.id }}</td>
                                <td>
                                    <strong>{{ customer.name }}</strong>
                                    {% if customer.address %}
                                    <br><small class="text-muted">{{ customer.address }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                        {{ customer.phone }}
                                    </a>
                                </td>
                                <td>{{ customer.national_id or '-' }}</td>
                                <td>
                                    {% if customer.email %}
                                    <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                        {{ customer.email }}
                                    </a>
                                    {% else %}
                                    -
                                    {% endif %}
                                </td>
                                <td>
                                    {% set debt = customer.get_total_debt() %}
                                    {% if debt > 0 %}
                                    <span class="badge bg-warning">{{ "{:,.0f}".format(debt) }} ر.س</span>
                                    {% else %}
                                    <span class="badge bg-success">لا توجد ديون</span>
                                    {% endif %}
                                </td>
                                <td>{{ customer.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('edit_customer', id=customer.id) }}" 
                                           class="btn btn-sm btn-outline-primary" 
                                           data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" 
                                           class="btn btn-sm btn-outline-info" 
                                           data-bs-toggle="tooltip" title="كشف حساب">
                                            <i class="fas fa-file-alt"></i>
                                        </a>
                                        <a href="{{ url_for('delete_customer', id=customer.id) }}" 
                                           class="btn btn-sm btn-outline-danger" 
                                           data-bs-toggle="tooltip" title="حذف"
                                           onclick="return confirmDelete('هل أنت متأكد من حذف هذا العميل؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- الصفحات -->
                {% if customers.pages > 1 %}
                <nav aria-label="صفحات العملاء">
                    <ul class="pagination justify-content-center">
                        {% if customers.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('customers', page=customers.prev_num, search=search) }}">
                                السابق
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in customers.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != customers.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('customers', page=page_num, search=search) }}">
                                        {{ page_num }}
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if customers.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('customers', page=customers.next_num, search=search) }}">
                                التالي
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد عملاء</h4>
                    <p class="text-muted">
                        {% if search %}
                        لم يتم العثور على عملاء مطابقين لبحثك
                        {% else %}
                        ابدأ بإضافة عميل جديد
                        {% endif %}
                    </p>
                    {% if not search %}
                    <a href="{{ url_for('add_customer') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        إضافة عميل جديد
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- أزرار إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex gap-2 flex-wrap">
            <button class="btn btn-outline-success" onclick="exportToCSV('customersTable', 'customers.csv')">
                <i class="fas fa-file-excel"></i>
                تصدير Excel
            </button>
            <button class="btn btn-outline-info btn-print">
                <i class="fas fa-print"></i>
                طباعة
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تهيئة التلميحات
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
