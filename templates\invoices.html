{% extends "base.html" %}

{% block title %}إدارة الفواتير - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-file-invoice text-primary"></i>
                إدارة الفواتير
            </h1>
            <a href="{{ url_for('add_invoice') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إنشاء فاتورة جديدة
            </a>
        </div>
    </div>
</div>

<!-- شريط البحث -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-8">
                        <input type="text" class="form-control" name="search" 
                               placeholder="البحث برقم الفاتورة أو اسم العميل أو ماركة الدراجة..." 
                               value="{{ search }}">
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-outline-primary w-100">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول الفواتير -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i>
                    قائمة الفواتير ({{ invoices.total }} فاتورة)
                </h5>
            </div>
            <div class="card-body">
                {% if invoices.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>الضامن</th>
                                <th>الدراجة</th>
                                <th>المبلغ الإجمالي</th>
                                <th>المبلغ المدفوع</th>
                                <th>المبلغ المتبقي</th>
                                <th>الحالة</th>
                                <th>تاريخ الفاتورة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in invoices.items %}
                            <tr>
                                <td>
                                    <strong class="text-primary">{{ invoice.invoice_number }}</strong>
                                </td>
                                <td>
                                    <strong>{{ invoice.customer.name }}</strong>
                                    <br><small class="text-muted">{{ invoice.customer.phone }}</small>
                                </td>
                                <td>
                                    {{ invoice.guarantor.name }}
                                    <br><small class="text-muted">{{ invoice.guarantor.phone }}</small>
                                </td>
                                <td>
                                    <strong>{{ invoice.motorcycle.brand }}</strong>
                                    <br><small class="text-muted">{{ invoice.motorcycle.model }}</small>
                                </td>
                                <td>
                                    <strong class="text-success">{{ "{:,.0f}".format(invoice.total_amount) }} ر.س</strong>
                                </td>
                                <td>
                                    <span class="text-info">{{ "{:,.0f}".format(invoice.get_paid_amount()) }} ر.س</span>
                                </td>
                                <td>
                                    {% set remaining = invoice.get_remaining_debt() %}
                                    {% if remaining > 0 %}
                                    <span class="text-warning">{{ "{:,.0f}".format(remaining) }} ر.س</span>
                                    {% else %}
                                    <span class="text-success">مكتمل</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if invoice.status == 'نشط' %}
                                    <span class="badge bg-success">{{ invoice.status }}</span>
                                    {% elif invoice.status == 'مكتمل' %}
                                    <span class="badge bg-primary">{{ invoice.status }}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ invoice.status }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('view_invoice', id=invoice.id) }}"
                                           class="btn btn-sm btn-outline-primary"
                                           data-bs-toggle="tooltip" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('edit_invoice', id=invoice.id) }}"
                                           class="btn btn-sm btn-outline-warning"
                                           data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#"
                                           class="btn btn-sm btn-outline-info"
                                           data-bs-toggle="tooltip" title="طباعة">
                                            <i class="fas fa-print"></i>
                                        </a>
                                        <a href="{{ url_for('view_invoice', id=invoice.id) }}"
                                           class="btn btn-sm btn-outline-success"
                                           data-bs-toggle="tooltip" title="الأقساط">
                                            <i class="fas fa-calendar-alt"></i>
                                        </a>
                                        <a href="{{ url_for('delete_invoice', id=invoice.id) }}"
                                           class="btn btn-sm btn-outline-danger"
                                           data-bs-toggle="tooltip" title="حذف"
                                           onclick="return confirmDelete('هل أنت متأكد من حذف هذه الفاتورة؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- الصفحات -->
                {% if invoices.pages > 1 %}
                <nav aria-label="صفحات الفواتير">
                    <ul class="pagination justify-content-center">
                        {% if invoices.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('invoices', page=invoices.prev_num, search=search) }}">
                                السابق
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in invoices.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != invoices.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('invoices', page=page_num, search=search) }}">
                                        {{ page_num }}
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if invoices.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('invoices', page=invoices.next_num, search=search) }}">
                                التالي
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد فواتير</h4>
                    <p class="text-muted">
                        {% if search %}
                        لم يتم العثور على فواتير مطابقة لبحثك
                        {% else %}
                        ابدأ بإنشاء فاتورة جديدة
                        {% endif %}
                    </p>
                    {% if not search %}
                    <a href="{{ url_for('add_invoice') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        إنشاء فاتورة جديدة
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- أزرار إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex gap-2 flex-wrap">
            <button class="btn btn-outline-success" onclick="exportToCSV('invoicesTable', 'invoices.csv')">
                <i class="fas fa-file-excel"></i>
                تصدير Excel
            </button>
            <button class="btn btn-outline-info btn-print">
                <i class="fas fa-print"></i>
                طباعة
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تهيئة التلميحات
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
