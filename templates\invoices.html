{% extends "base.html" %}

{% block title %}إدارة الفواتير - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-file-invoice text-primary"></i>
                إدارة الفواتير
            </h1>
            <a href="{{ url_for('add_invoice') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إنشاء فاتورة جديدة
            </a>
        </div>
    </div>
</div>

<!-- شريط البحث -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-8">
                        <input type="text" class="form-control" name="search" 
                               placeholder="البحث برقم الفاتورة أو اسم العميل أو ماركة الدراجة..." 
                               value="{{ search }}">
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-outline-primary w-100">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول الفواتير -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i>
                    قائمة الفواتير ({{ invoices.total }} فاتورة)
                </h5>
            </div>
            <div class="card-body">
                {% if invoices.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>الضامن</th>
                                <th>الدراجة</th>
                                <th>المبلغ الإجمالي</th>
                                <th>المبلغ المدفوع</th>
                                <th>المبلغ المتبقي</th>
                                <th>الحالة</th>
                                <th>تاريخ الفاتورة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in invoices.items %}
                            <tr>
                                <td>
                                    <strong class="text-primary">{{ invoice.invoice_number }}</strong>
                                </td>
                                <td>
                                    <strong>{{ invoice.customer.name }}</strong>
                                    <br><small class="text-muted">{{ invoice.customer.phone }}</small>
                                </td>
                                <td>
                                    {{ invoice.guarantor.name }}
                                    <br><small class="text-muted">{{ invoice.guarantor.phone }}</small>
                                </td>
                                <td>
                                    <strong>{{ invoice.motorcycle.brand }}</strong>
                                    <br><small class="text-muted">{{ invoice.motorcycle.model }}</small>
                                </td>
                                <td>
                                    <strong class="text-success">{{ "{:,.0f}".format(invoice.total_amount) }} ر.س</strong>
                                </td>
                                <td>
                                    <span class="text-info">{{ "{:,.0f}".format(invoice.get_paid_amount()) }} ر.س</span>
                                </td>
                                <td>
                                    {% set remaining = invoice.get_remaining_debt() %}
                                    {% if remaining > 0 %}
                                    <span class="text-warning">{{ "{:,.0f}".format(remaining) }} ر.س</span>
                                    {% else %}
                                    <span class="text-success">مكتمل</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if invoice.status == 'نشط' %}
                                    <span class="badge bg-success">{{ invoice.status }}</span>
                                    {% elif invoice.status == 'مكتمل' %}
                                    <span class="badge bg-primary">{{ invoice.status }}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ invoice.status }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-outline-primary"
                                                onclick="viewInvoice({{ invoice.id }})"
                                                data-bs-toggle="tooltip" title="عرض الفاتورة">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-info"
                                                onclick="printInvoice({{ invoice.id }})"
                                                data-bs-toggle="tooltip" title="طباعة الفاتورة">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-success"
                                                onclick="viewInstallments({{ invoice.id }})"
                                                data-bs-toggle="tooltip" title="عرض الأقساط">
                                            <i class="fas fa-calendar-alt"></i>
                                        </button>
                                        <a href="{{ url_for('edit_invoice', id=invoice.id) }}"
                                           class="btn btn-sm btn-outline-warning"
                                           data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('delete_invoice', id=invoice.id) }}"
                                           class="btn btn-sm btn-outline-danger"
                                           data-bs-toggle="tooltip" title="حذف"
                                           onclick="return confirmDelete('هل أنت متأكد من حذف هذه الفاتورة؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- الصفحات -->
                {% if invoices.pages > 1 %}
                <nav aria-label="صفحات الفواتير">
                    <ul class="pagination justify-content-center">
                        {% if invoices.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('invoices', page=invoices.prev_num, search=search) }}">
                                السابق
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in invoices.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != invoices.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('invoices', page=page_num, search=search) }}">
                                        {{ page_num }}
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if invoices.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('invoices', page=invoices.next_num, search=search) }}">
                                التالي
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد فواتير</h4>
                    <p class="text-muted">
                        {% if search %}
                        لم يتم العثور على فواتير مطابقة لبحثك
                        {% else %}
                        ابدأ بإنشاء فاتورة جديدة
                        {% endif %}
                    </p>
                    {% if not search %}
                    <a href="{{ url_for('add_invoice') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        إنشاء فاتورة جديدة
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- أزرار إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex gap-2 flex-wrap">
            <button class="btn btn-outline-success" onclick="exportToCSV('invoicesTable', 'invoices.csv')">
                <i class="fas fa-file-excel"></i>
                تصدير Excel
            </button>
            <button class="btn btn-outline-info btn-print">
                <i class="fas fa-print"></i>
                طباعة
            </button>
        </div>
    </div>
</div>

<!-- نافذة عرض الفاتورة -->
<div class="modal fade" id="invoiceModal" tabindex="-1" aria-labelledby="invoiceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="invoiceModalLabel">
                    <i class="fas fa-file-invoice text-primary"></i>
                    تفاصيل الفاتورة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body" id="invoiceModalBody">
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل بيانات الفاتورة...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-info" onclick="printCurrentInvoice()">
                    <i class="fas fa-print"></i>
                    طباعة
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة عرض الأقساط -->
<div class="modal fade" id="installmentsModal" tabindex="-1" aria-labelledby="installmentsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="installmentsModalLabel">
                    <i class="fas fa-calendar-alt text-success"></i>
                    جدول الأقساط
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body" id="installmentsModalBody">
                <div class="text-center py-4">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل بيانات الأقساط...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-success" onclick="printInstallments()">
                    <i class="fas fa-print"></i>
                    طباعة جدول الأقساط
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentInvoiceId = null;

// دالة مساعدة للتعامل مع القيم الفارغة
function safeValue(value, defaultValue = 'غير محدد') {
    return value && value !== '' && value !== null && value !== undefined ? value : defaultValue;
}

// دالة مساعدة لتنسيق الأرقام
function formatNumber(number) {
    return number ? parseFloat(number).toLocaleString() : '0';
}

// تهيئة التلميحات
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// عرض الفاتورة
async function viewInvoice(invoiceId) {
    currentInvoiceId = invoiceId;
    const modal = new bootstrap.Modal(document.getElementById('invoiceModal'));
    const modalBody = document.getElementById('invoiceModalBody');

    // إظهار النافذة مع رسالة التحميل
    modal.show();

    try {
        const response = await fetch(`/api/invoice/${invoiceId}`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const invoice = await response.json();

        if (invoice.error) {
            throw new Error(invoice.error);
        }

        modalBody.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0">معلومات الفاتورة</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>رقم الفاتورة:</strong></td>
                                    <td>${invoice.invoice_number}</td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ الفاتورة:</strong></td>
                                    <td>${invoice.invoice_date}</td>
                                </tr>
                                <tr>
                                    <td><strong>الحالة:</strong></td>
                                    <td><span class="badge bg-${invoice.status === 'نشط' ? 'success' : 'secondary'}">${invoice.status}</span></td>
                                </tr>
                                <tr>
                                    <td><strong>العملة:</strong></td>
                                    <td>${invoice.currency_symbol}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">معلومات العميل والضامن</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>العميل:</strong></td>
                                    <td>${invoice.customer_name}</td>
                                </tr>
                                <tr>
                                    <td><strong>هاتف العميل:</strong></td>
                                    <td>${invoice.customer_phone}</td>
                                </tr>
                                <tr>
                                    <td><strong>الضامن:</strong></td>
                                    <td>${invoice.guarantor_name}</td>
                                </tr>
                                <tr>
                                    <td><strong>هاتف الضامن:</strong></td>
                                    <td>${invoice.guarantor_phone}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">معلومات الدراجة</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>الماركة:</strong></td>
                                    <td>${invoice.motorcycle_brand}</td>
                                </tr>
                                <tr>
                                    <td><strong>الموديل:</strong></td>
                                    <td>${invoice.motorcycle_model}</td>
                                </tr>
                                <tr>
                                    <td><strong>السنة:</strong></td>
                                    <td>${invoice.motorcycle_year || 'غير محدد'}</td>
                                </tr>
                                <tr>
                                    <td><strong>اللون:</strong></td>
                                    <td>${invoice.motorcycle_color || 'غير محدد'}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-warning text-white">
                            <h6 class="mb-0">المبالغ المالية</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>المبلغ الإجمالي:</strong></td>
                                    <td class="text-primary"><strong>${formatNumber(invoice.total_amount)} ${safeValue(invoice.currency_symbol, 'ر.س')}</strong></td>
                                </tr>
                                <tr>
                                    <td><strong>الدفعة المقدمة:</strong></td>
                                    <td>${formatNumber(invoice.down_payment)} ${safeValue(invoice.currency_symbol, 'ر.س')}</td>
                                </tr>
                                <tr>
                                    <td><strong>المبلغ المتبقي:</strong></td>
                                    <td class="text-warning"><strong>${formatNumber(invoice.remaining_amount)} ${safeValue(invoice.currency_symbol, 'ر.س')}</strong></td>
                                </tr>
                                <tr>
                                    <td><strong>قيمة القسط:</strong></td>
                                    <td>${formatNumber(invoice.installment_amount)} ${safeValue(invoice.currency_symbol, 'ر.س')}</td>
                                </tr>
                                <tr>
                                    <td><strong>عدد الأقساط:</strong></td>
                                    <td>${invoice.installments_count} قسط</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            ${invoice.notes ? `
            <div class="row mt-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-secondary text-white">
                            <h6 class="mb-0">ملاحظات</h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-0">${invoice.notes}</p>
                        </div>
                    </div>
                </div>
            </div>
            ` : ''}
        `;

    } catch (error) {
        modalBody.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                خطأ في تحميل بيانات الفاتورة: ${error.message}
            </div>
        `;
    }
}

// عرض الأقساط
async function viewInstallments(invoiceId) {
    const modal = new bootstrap.Modal(document.getElementById('installmentsModal'));
    const modalBody = document.getElementById('installmentsModalBody');

    // إظهار النافذة مع رسالة التحميل
    modal.show();

    try {
        const response = await fetch(`/api/invoice/${invoiceId}/installments`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.error) {
            throw new Error(data.error);
        }

        modalBody.innerHTML = `
            <div class="row mb-3">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h5>${data.invoice.invoice_number}</h5>
                            <p class="mb-0">رقم الفاتورة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h5>${data.paid_installments}</h5>
                            <p class="mb-0">أقساط مدفوعة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h5>${data.pending_installments}</h5>
                            <p class="mb-0">أقساط معلقة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h5>${data.overdue_installments}</h5>
                            <p class="mb-0">أقساط متأخرة</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>رقم القسط</th>
                            <th>المبلغ</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>المبلغ المدفوع</th>
                            <th>تاريخ الدفع</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.installments && data.installments.length > 0 ? data.installments.map(installment => `
                            <tr class="${installment.is_overdue ? 'table-danger' : installment.is_paid ? 'table-success' : ''}">
                                <td><strong>${safeValue(installment.installment_number, '0')}</strong></td>
                                <td>${formatNumber(installment.amount)} ${safeValue(data.invoice.currency_symbol, 'ر.س')}</td>
                                <td>${safeValue(installment.due_date, '-')}</td>
                                <td>${installment.paid_amount > 0 ? formatNumber(installment.paid_amount) + ' ' + safeValue(data.invoice.currency_symbol, 'ر.س') : '-'}</td>
                                <td>${safeValue(installment.payment_date, '-')}</td>
                                <td>
                                    ${installment.is_paid ?
                                        '<span class="badge bg-success">مدفوع</span>' :
                                        installment.is_overdue ?
                                            '<span class="badge bg-danger">متأخر</span>' :
                                            '<span class="badge bg-warning">معلق</span>'
                                    }
                                </td>
                            </tr>
                        `).join('') : '<tr><td colspan="6" class="text-center text-muted">لا توجد أقساط</td></tr>'}
                    </tbody>
                </table>
            </div>
        `;

    } catch (error) {
        modalBody.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                خطأ في تحميل بيانات الأقساط: ${error.message}
            </div>
        `;
    }
}

// طباعة الفاتورة
function printInvoice(invoiceId) {
    window.open(`/invoices/print/${invoiceId}`, '_blank');
}

// طباعة الفاتورة الحالية من النافذة المنبثقة
function printCurrentInvoice() {
    if (currentInvoiceId) {
        printInvoice(currentInvoiceId);
    }
}

// طباعة جدول الأقساط
function printInstallments() {
    const printContent = document.getElementById('installmentsModalBody').innerHTML;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>جدول الأقساط</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                @media print {
                    .btn { display: none; }
                }
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
                .table { font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="container-fluid">
                <h2 class="text-center mb-4">جدول الأقساط</h2>
                ${printContent}
            </div>
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// تأكيد الحذف
function confirmDelete(message) {
    return confirm(message);
}
</script>
{% endblock %}
