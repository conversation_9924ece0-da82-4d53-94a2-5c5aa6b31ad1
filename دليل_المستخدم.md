# دليل المستخدم - نظام محاسبة مبيعات الدراجات النارية

## 🚀 البدء السريع

### تشغيل النظام
1. **على الويندوز**: انقر نقراً مزدوجاً على ملف `run.bat`
2. **يدوياً**: افتح موجه الأوامر واكتب `python app.py`
3. **في المتصفح**: انتقل إلى `http://127.0.0.1:5000`

### أول استخدام
عند تشغيل النظام لأول مرة، ستجد بيانات تجريبية جاهزة للاستكشاف.

---

## 📱 تثبيت التطبيق على الهاتف

### للأندرويد (Chrome)
1. افتح الموقع في متصفح Chrome
2. اضغط على القائمة (⋮) ← "إضافة إلى الشاشة الرئيسية"
3. اختر اسماً للتطبيق واضغط "إضافة"

### للآيفون (Safari)
1. افتح الموقع في متصفح Safari
2. اضغط على زر المشاركة (□↗)
3. اختر "إضافة إلى الشاشة الرئيسية"

---

## 🏠 الصفحة الرئيسية

### الإحصائيات السريعة
- **إجمالي العملاء**: عدد العملاء المسجلين
- **إجمالي الفواتير**: عدد فواتير المبيعات
- **إجمالي المبيعات**: قيمة المبيعات بالريال
- **أقساط متأخرة**: عدد الأقساط المتأخرة

### الوصول السريع
أزرار سريعة للوصول لأهم الوظائف:
- إضافة عميل جديد
- إضافة ضامن جديد
- إضافة دراجة نارية
- إنشاء فاتورة جديدة
- سند قبض جديد
- عرض التقارير

---

## 👥 إدارة العملاء

### إضافة عميل جديد
1. انقر على "العملاء" من القائمة الرئيسية
2. اضغط "إضافة عميل جديد"
3. املأ البيانات المطلوبة:
   - **الاسم الكامل** (مطلوب)
   - **رقم الهاتف** (مطلوب - يبدأ بـ 05)
   - **رقم الهوية** (اختياري - 10 أرقام)
   - **البريد الإلكتروني** (اختياري)
   - **العنوان** (اختياري)
4. اضغط "حفظ العميل"

### البحث عن العملاء
- استخدم مربع البحث للعثور على عميل
- يمكن البحث بالاسم أو رقم الهاتف أو رقم الهوية
- النتائج تظهر فورياً أثناء الكتابة

### تعديل بيانات العميل
1. ابحث عن العميل في قائمة العملاء
2. اضغط على أيقونة التعديل (✏️)
3. عدّل البيانات المطلوبة
4. اضغط "حفظ التعديلات"

---

## 🛡️ إدارة الضامنين

### إضافة ضامن جديد
1. انقر على "الضامنين" من القائمة
2. اضغط "إضافة ضامن جديد"
3. املأ البيانات:
   - **الاسم الكامل** (مطلوب)
   - **رقم الهاتف** (مطلوب)
   - **رقم الهوية** (اختياري)
   - **العلاقة بالعميل** (والد، أخ، عم، إلخ)
   - **العنوان** (اختياري)

### أنواع العلاقات المتاحة
- والد
- أخ
- عم
- خال
- صديق
- زميل
- أخرى

---

## 🏍️ إدارة الدراجات النارية

### إضافة دراجة جديدة
1. انقر على "الدراجات" من القائمة
2. اضغط "إضافة دراجة جديدة"
3. املأ البيانات:
   - **الماركة** (مطلوب - هوندا، ياماها، سوزوكي، إلخ)
   - **الموديل** (مطلوب - مثل CBR 150)
   - **سنة الصنع** (اختياري)
   - **حجم المحرك** (اختياري - 125cc، 150cc، إلخ)
   - **اللون** (اختياري)
   - **السعر** (مطلوب بالريال السعودي)

### الماركات المتاحة
- هوندا (Honda)
- ياماها (Yamaha)
- سوزوكي (Suzuki)
- كاواساكي (Kawasaki)
- دوكاتي (Ducati)
- بي إم دبليو (BMW)
- هارلي ديفيدسون (Harley-Davidson)

---

## 📄 إنشاء الفواتير

### خطوات إنشاء فاتورة جديدة
1. انقر على "الفواتير" ← "إنشاء فاتورة جديدة"
2. اختر **العميل** من القائمة المنسدلة
3. اختر **الضامن** من القائمة المنسدلة
4. اختر **الدراجة النارية** (سيتم تعبئة السعر تلقائياً)
5. حدد **الدفعة المقدمة** (اختياري)
6. اختر **عدد الأقساط** (6، 12، 18، 24، أو 36 قسط)
7. حدد **تاريخ أول قسط**
8. أضف **ملاحظات** إضافية (اختياري)
9. اضغط "إنشاء الفاتورة"

### الحسابات التلقائية
- **المبلغ المتبقي** = المبلغ الإجمالي - الدفعة المقدمة
- **قيمة القسط** = المبلغ المتبقي ÷ عدد الأقساط
- **تواريخ الأقساط** تُحسب تلقائياً (كل 30 يوم)

---

## 💰 إدارة الأقساط

### تتبع الأقساط
- كل فاتورة تُنشئ جدول أقساط تلقائياً
- يمكن مراجعة حالة كل قسط (مدفوع، مستحق، متأخر)
- التنبيهات تظهر للأقساط المتأخرة

### حالات الأقساط
- **مدفوع** (أخضر): تم السداد
- **مستحق** (أصفر): حان موعد السداد
- **متأخر** (أحمر): تجاوز موعد السداد

---

## 🧾 سندات القبض

### إصدار سند قبض
1. انقر على "سندات القبض"
2. اضغط "إضافة سند قبض"
3. اختر **العميل**
4. أدخل **المبلغ المستلم**
5. اختر **طريقة الدفع**:
   - نقدي
   - تحويل بنكي
   - شيك
   - بطاقة ائتمان
6. حدد **تاريخ الاستلام**
7. أضف **ملاحظات** (اختياري)
8. اضغط "حفظ السند"

### طباعة السندات
- يمكن طباعة أي سند قبض
- التصميم مُحسّن للطباعة على ورق A4
- يحتوي على جميع البيانات المطلوبة

---

## 📊 التقارير والإحصائيات

### أنواع التقارير المتاحة

#### 1. تقرير المبيعات الإجمالية
- إجمالي المبيعات بالريال
- المبلغ المحصل والمتبقي
- عدد الفواتير
- رسم بياني للمبيعات الشهرية

#### 2. تقرير العملاء
- قائمة بجميع العملاء
- إجمالي مشتريات كل عميل
- المبالغ المدفوعة والمتبقية
- حالة كل عميل

#### 3. تقرير الأقساط المتأخرة
- العملاء المتأخرين في السداد
- المبالغ المستحقة
- عدد أيام التأخير
- تنبيهات ملونة حسب درجة التأخير

#### 4. تقرير مبيعات الدراجات
- المبيعات حسب الماركة
- الموديلات الأكثر مبيعاً
- رسم بياني دائري للتوزيع
- إحصائيات مفصلة

#### 5. كشف حساب الضامنين
- قائمة الضامنين
- العملاء المرتبطين بكل ضامن
- المبالغ المضمونة
- الحالة المالية

#### 6. تقرير الأقساط المستحقة
- الأقساط المدفوعة والمستحقة
- جدولة زمنية للاستحقاقات
- إحصائيات ملونة
- تفاصيل كل قسط

### تصدير وطباعة التقارير
- **تصدير Excel**: لحفظ البيانات في ملف Excel
- **طباعة**: لطباعة التقرير مباشرة
- **عرض تفاعلي**: مع رسوم بيانية ملونة

---

## 🔧 نصائح وحيل

### تحسين الأداء
- استخدم البحث السريع بدلاً من التصفح
- راجع التقارير بانتظام لمتابعة الأداء
- احرص على تحديث بيانات الاتصال

### أفضل الممارسات
- **النسخ الاحتياطية**: انسخ ملف قاعدة البيانات بانتظام
- **التحديث المستمر**: حدّث بيانات العملاء عند تغيير أرقام الهواتف
- **المراجعة الدورية**: راجع الأقساط المتأخرة أسبوعياً

### اختصارات لوحة المفاتيح
- **Ctrl + F**: البحث في الصفحة
- **Ctrl + P**: طباعة
- **F5**: تحديث الصفحة
- **Alt + ←**: العودة للصفحة السابقة

---

## ❓ الأسئلة الشائعة

### س: كيف أغير عدد الأقساط بعد إنشاء الفاتورة؟
**ج:** حالياً لا يمكن تعديل عدد الأقساط بعد إنشاء الفاتورة. يُنصح بالتأكد من العدد قبل الحفظ.

### س: هل يمكن حذف فاتورة؟
**ج:** نعم، يمكن حذف الفاتورة من قائمة الفواتير، لكن احذر لأن هذا سيحذف جميع الأقساط المرتبطة بها.

### س: كيف أضيف ماركة دراجة غير موجودة؟
**ج:** اختر "أخرى" من قائمة الماركات وأدخل الاسم الجديد.

### س: هل البيانات محفوظة بأمان؟
**ج:** نعم، جميع البيانات محفوظة في قاعدة بيانات محلية على جهازك.

### س: كيف أنسخ البيانات لجهاز آخر؟
**ج:** انسخ ملف `motorcycle_sales.db` إلى الجهاز الآخر في نفس مجلد التطبيق.

---

## 🆘 الدعم الفني

### مشاكل شائعة وحلولها

#### التطبيق لا يعمل
1. تأكد من تثبيت Python
2. تأكد من تثبيت المتطلبات: `pip install -r requirements.txt`
3. تأكد من عدم استخدام المنفذ 5000 من تطبيق آخر

#### البيانات لا تظهر
1. تأكد من وجود ملف قاعدة البيانات
2. أعد تشغيل التطبيق
3. تحقق من وجود أخطاء في نافذة الأوامر

#### مشاكل في التصميم
1. تأكد من اتصال الإنترنت (للخطوط والأيقونات)
2. امسح ذاكرة التخزين المؤقت للمتصفح
3. جرب متصفحاً آخر

### طلب المساعدة
إذا واجهت مشكلة لم تُذكر هنا:
1. تأكد من اتباع التعليمات بدقة
2. راجع ملف README.md
3. تواصل مع الدعم الفني

---

**نتمنى لك تجربة ممتعة ومفيدة مع نظام محاسبة مبيعات الدراجات النارية! 🏍️**
