{% extends "base.html" %}

{% block title %}إدارة سندات القبض - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-receipt text-primary"></i>
                إدارة سندات القبض
            </h1>
            <a href="#" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addReceiptModal">
                <i class="fas fa-plus"></i>
                إضافة سند قبض
            </a>
        </div>
    </div>
</div>

<!-- جدول سندات القبض -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i>
                    قائمة سندات القبض ({{ receipts.total }} سند)
                </h5>
            </div>
            <div class="card-body">
                {% if receipts.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم السند</th>
                                <th>العميل</th>
                                <th>رقم الفاتورة</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>تاريخ الاستلام</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for receipt in receipts.items %}
                            <tr>
                                <td>
                                    <strong class="text-primary">{{ receipt.receipt_number }}</strong>
                                </td>
                                <td>
                                    <strong>{{ receipt.customer.name }}</strong>
                                    <br><small class="text-muted">{{ receipt.customer.phone }}</small>
                                </td>
                                <td>
                                    {% if receipt.invoice_id %}
                                    <a href="#" class="text-decoration-none">
                                        {{ receipt.invoice.invoice_number }}
                                    </a>
                                    {% else %}
                                    -
                                    {% endif %}
                                </td>
                                <td>
                                    <strong class="text-success">{{ "{:,.0f}".format(receipt.amount or 0) }}
                                    {{ receipt.currency_ref.symbol if receipt.currency_ref else 'ر.س' }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ receipt.payment_method }}</span>
                                </td>
                                <td>{{ receipt.receipt_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="#" 
                                           class="btn btn-sm btn-outline-primary" 
                                           data-bs-toggle="tooltip" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" 
                                           class="btn btn-sm btn-outline-info" 
                                           data-bs-toggle="tooltip" title="طباعة">
                                            <i class="fas fa-print"></i>
                                        </a>
                                        <a href="{{ url_for('delete_receipt', id=receipt.id) }}"
                                           class="btn btn-sm btn-outline-danger"
                                           data-bs-toggle="tooltip" title="حذف"
                                           onclick="return confirmDelete('هل أنت متأكد من حذف هذا السند؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- الصفحات -->
                {% if receipts.pages > 1 %}
                <nav aria-label="صفحات سندات القبض">
                    <ul class="pagination justify-content-center">
                        {% if receipts.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('receipts', page=receipts.prev_num) }}">
                                السابق
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in receipts.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != receipts.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('receipts', page=page_num) }}">
                                        {{ page_num }}
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if receipts.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('receipts', page=receipts.next_num) }}">
                                التالي
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد سندات قبض</h4>
                    <p class="text-muted">ابدأ بإضافة سند قبض جديد</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addReceiptModal">
                        <i class="fas fa-plus"></i>
                        إضافة سند قبض
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة سند قبض -->
<div class="modal fade" id="addReceiptModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-receipt"></i>
                    إضافة سند قبض جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="receiptForm" method="POST" action="{{ url_for('add_receipt') }}">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="customer_id" class="form-label required">العميل</label>
                            <select class="form-select" id="customer_id" name="customer_id" required>
                                <option value="">اختر العميل</option>
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="amount" class="form-label required">المبلغ</label>
                            <input type="number" class="form-control" id="amount" name="amount"
                                   min="1" step="0.01" required>
                            <div class="invalid-feedback">
                                يرجى إدخال المبلغ
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="currency_id" class="form-label required">العملة</label>
                            <select class="form-select" id="currency_id" name="currency_id" required>
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار العملة
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="payment_method" class="form-label">طريقة الدفع</label>
                            <select class="form-select" id="payment_method" name="payment_method">
                                <option value="نقدي" selected>نقدي</option>
                                <option value="تحويل بنكي">تحويل بنكي</option>
                                <option value="شيك">شيك</option>
                                <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="receipt_date" class="form-label">تاريخ الاستلام</label>
                            <input type="date" class="form-control" id="receipt_date" name="receipt_date">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="submit" form="receiptForm" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    حفظ السند
                </button>
            </div>
        </div>
    </div>
</div>

<!-- أزرار إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex gap-2 flex-wrap">
            <button class="btn btn-outline-success" onclick="exportToCSV('receiptsTable', 'receipts.csv')">
                <i class="fas fa-file-excel"></i>
                تصدير Excel
            </button>
            <button class="btn btn-outline-info btn-print">
                <i class="fas fa-print"></i>
                طباعة
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تهيئة التلميحات
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // تعيين تاريخ اليوم
    document.getElementById('receipt_date').value = new Date().toISOString().split('T')[0];

    // تحميل قائمة العملاء والعملات
    loadCustomers();
    loadCurrencies();
});

function loadCustomers() {
    fetch('/api/customers')
        .then(response => response.json())
        .then(customers => {
            const customerSelect = document.getElementById('customer_id');
            customerSelect.innerHTML = '<option value="">اختر العميل</option>';

            customers.forEach(customer => {
                const option = document.createElement('option');
                option.value = customer.id;
                option.textContent = `${customer.name} - ${customer.phone}`;
                if (customer.debt > 0) {
                    option.textContent += ` (مديون: ${customer.debt.toLocaleString()} ر.س)`;
                }
                customerSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('خطأ في تحميل العملاء:', error);
        });
}

function loadCurrencies() {
    fetch('/api/currencies')
        .then(response => response.json())
        .then(currencies => {
            const currencySelect = document.getElementById('currency_id');
            currencySelect.innerHTML = '<option value="">اختر العملة</option>';

            currencies.forEach(currency => {
                const option = document.createElement('option');
                option.value = currency.id;
                option.textContent = `${currency.name} (${currency.symbol})`;
                if (currency.is_base) {
                    option.selected = true;
                }
                currencySelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('خطأ في تحميل العملات:', error);
        });
}
</script>
{% endblock %}
