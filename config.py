import os

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'motorcycle-sales-system-2024'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///motorcycle_sales.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # إعدادات التطبيق
    APP_NAME = "نظام محاسبة مبيعات الدراجات النارية"
    COMPANY_NAME = "شركة الدراجات النارية"
    
    # إعدادات الطباعة
    PRINT_LOGO_PATH = "static/images/logo.png"
    
    # إعدادات التقارير
    REPORTS_PER_PAGE = 20
    
    # إعدادات العملة
    CURRENCY = "ريال"
    CURRENCY_SYMBOL = "ر.س"
