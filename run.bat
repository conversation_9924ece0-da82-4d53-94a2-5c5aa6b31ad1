@echo off
chcp 65001 > nul
title نظام محاسبة مبيعات الدراجات النارية

echo.
echo ========================================
echo    نظام محاسبة مبيعات الدراجات النارية
echo ========================================
echo.

echo جاري فحص متطلبات النظام...
python --version > nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من: https://python.org
    pause
    exit /b 1
)

echo تم العثور على Python بنجاح
echo.

echo جاري تثبيت المتطلبات...
pip install -r requirements.txt > nul 2>&1
if errorlevel 1 (
    echo تحذير: قد تكون هناك مشكلة في تثبيت بعض المتطلبات
)

echo.
echo جاري تشغيل النظام...
echo.
echo ملاحظة: سيتم فتح النظام في المتصفح تلقائياً
echo للإغلاق: اضغط Ctrl+C في هذه النافذة
echo.

timeout /t 3 > nul

start http://127.0.0.1:5000

python app.py

pause
