#!/usr/bin/env python3
"""
إصلاح قاعدة البيانات لإضافة دعم العملات
"""

import sqlite3
import os
from datetime import datetime

def find_database():
    """البحث عن قاعدة البيانات"""
    db_paths = ['motorcycle_sales.db', 'instance/motorcycle_sales.db']
    
    for path in db_paths:
        if os.path.exists(path):
            print(f"تم العثور على قاعدة البيانات: {path}")
            return path
    
    print("لم يتم العثور على قاعدة البيانات!")
    return None

def get_table_names(cursor):
    """الحصول على أسماء الجداول"""
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = [table[0] for table in cursor.fetchall()]
    return tables

def check_column_exists(cursor, table_name, column_name):
    """التحقق من وجود عمود في جدول"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = [column[1] for column in cursor.fetchall()]
    return column_name in columns

def fix_database():
    """إصلاح قاعدة البيانات"""
    
    # البحث عن قاعدة البيانات
    db_path = find_database()
    if not db_path:
        return False
    
    # إنشاء نسخة احتياطية
    backup_name = f'backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
    if os.name == 'nt':  # Windows
        os.system(f'copy "{db_path}" "{backup_name}"')
    else:  # Unix/Linux
        os.system(f'cp "{db_path}" "{backup_name}"')
    print(f"تم إنشاء نسخة احتياطية: {backup_name}")
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("بدء إصلاح قاعدة البيانات...")
        
        # الحصول على أسماء الجداول
        tables = get_table_names(cursor)
        print(f"الجداول الموجودة: {tables}")
        
        # 1. إنشاء جدول العملات
        print("إنشاء جدول العملات...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS currencies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                code VARCHAR(10) NOT NULL UNIQUE,
                symbol VARCHAR(10) NOT NULL,
                exchange_rate FLOAT NOT NULL DEFAULT 1.0,
                is_base BOOLEAN DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 2. إضافة العملات الافتراضية
        print("إضافة العملات الافتراضية...")
        cursor.execute('SELECT COUNT(*) FROM currencies')
        if cursor.fetchone()[0] == 0:
            currencies = [
                ('الريال السعودي', 'SAR', 'ر.س', 1.0, 1, 1),
                ('الدولار الأمريكي', 'USD', '$', 3.75, 0, 1),
                ('اليورو', 'EUR', '€', 4.10, 0, 1),
                ('الجنيه الإسترليني', 'GBP', '£', 4.75, 0, 1),
                ('الدرهم الإماراتي', 'AED', 'د.إ', 1.02, 0, 1)
            ]
            
            cursor.executemany('''
                INSERT INTO currencies (name, code, symbol, exchange_rate, is_base, is_active)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', currencies)
            print("تم إضافة العملات الافتراضية")
        
        # 3. إضافة أعمدة العملة للجداول الموجودة
        tables_to_update = []
        
        # البحث عن الجداول التي تحتاج تحديث
        for table in tables:
            if table in ['customers', 'guarantors', 'motorcycles', 'invoices', 'receipts']:
                tables_to_update.append(table)
        
        print(f"الجداول التي ستُحدث: {tables_to_update}")
        
        for table in tables_to_update:
            if not check_column_exists(cursor, table, 'currency_id'):
                print(f"إضافة عمود العملة لجدول {table}...")
                cursor.execute(f'ALTER TABLE {table} ADD COLUMN currency_id INTEGER DEFAULT 1')
                cursor.execute(f'UPDATE {table} SET currency_id = 1 WHERE currency_id IS NULL')
        
        # 4. حفظ التغييرات
        conn.commit()
        print("تم إصلاح قاعدة البيانات بنجاح!")
        
        # 5. التحقق من النتائج
        cursor.execute('SELECT COUNT(*) FROM currencies')
        currency_count = cursor.fetchone()[0]
        print(f"عدد العملات: {currency_count}")
        
        return True
        
    except Exception as e:
        print(f"خطأ في إصلاح قاعدة البيانات: {e}")
        conn.rollback()
        return False
        
    finally:
        conn.close()

def verify_fix():
    """التحقق من الإصلاح"""
    db_path = find_database()
    if not db_path:
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("\nالتحقق من الإصلاح...")
        
        # فحص جدول العملات
        cursor.execute('SELECT COUNT(*) FROM currencies')
        currency_count = cursor.fetchone()[0]
        print(f"✓ جدول العملات: {currency_count} عملة")
        
        # فحص الجداول الأخرى
        tables = get_table_names(cursor)
        tables_to_check = [t for t in tables if t in ['customers', 'guarantors', 'motorcycles', 'invoices', 'receipts']]
        
        for table in tables_to_check:
            if check_column_exists(cursor, table, 'currency_id'):
                print(f"✓ جدول {table}: يحتوي على عمود currency_id")
            else:
                print(f"✗ جدول {table}: لا يحتوي على عمود currency_id")
        
        return True
        
    except Exception as e:
        print(f"خطأ في التحقق: {e}")
        return False
        
    finally:
        conn.close()

if __name__ == '__main__':
    print("=" * 50)
    print("إصلاح قاعدة البيانات لدعم العملات")
    print("=" * 50)
    
    if fix_database():
        verify_fix()
        print("\n" + "=" * 50)
        print("تم إصلاح قاعدة البيانات بنجاح!")
        print("يمكنك الآن تشغيل التطبيق")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("فشل في إصلاح قاعدة البيانات!")
        print("=" * 50)
