#!/usr/bin/env python3
"""
اختبار النماذج للتأكد من عدم وجود أخطاء في العمليات الحسابية
"""

from app import app
from models import db, Customer, Invoice, Installment, Receipt
import traceback

def test_customer_debt():
    """اختبار حساب ديون العميل"""
    with app.app_context():
        try:
            customers = Customer.query.all()
            print(f"عدد العملاء: {len(customers)}")
            
            for customer in customers:
                try:
                    debt = customer.get_total_debt()
                    print(f"العميل {customer.name}: الدين = {debt}")
                except Exception as e:
                    print(f"خطأ في حساب دين العميل {customer.name}: {e}")
                    traceback.print_exc()
                    
        except Exception as e:
            print(f"خطأ عام في اختبار العملاء: {e}")
            traceback.print_exc()

def test_invoice_calculations():
    """اختبار حسابات الفواتير"""
    with app.app_context():
        try:
            invoices = Invoice.query.all()
            print(f"عدد الفواتير: {len(invoices)}")
            
            for invoice in invoices:
                try:
                    paid = invoice.get_paid_amount()
                    remaining = invoice.get_remaining_debt()
                    print(f"الفاتورة {invoice.invoice_number}: مدفوع = {paid}, متبقي = {remaining}")
                except Exception as e:
                    print(f"خطأ في حساب الفاتورة {invoice.invoice_number}: {e}")
                    traceback.print_exc()
                    
        except Exception as e:
            print(f"خطأ عام في اختبار الفواتير: {e}")
            traceback.print_exc()

def test_relationships():
    """اختبار العلاقات بين النماذج"""
    with app.app_context():
        try:
            customers = Customer.query.all()
            
            for customer in customers:
                try:
                    print(f"العميل {customer.name}:")
                    print(f"  - عدد الفواتير: {len(customer.invoices) if customer.invoices else 0}")
                    print(f"  - عدد سندات القبض: {len(customer.receipts) if customer.receipts else 0}")
                    
                    # اختبار كل فاتورة
                    if customer.invoices:
                        for invoice in customer.invoices:
                            print(f"    فاتورة {invoice.invoice_number}: {len(invoice.installments) if invoice.installments else 0} قسط")
                            
                except Exception as e:
                    print(f"خطأ في اختبار علاقات العميل {customer.name}: {e}")
                    traceback.print_exc()
                    
        except Exception as e:
            print(f"خطأ عام في اختبار العلاقات: {e}")
            traceback.print_exc()

if __name__ == '__main__':
    print("=" * 50)
    print("اختبار النماذج والحسابات")
    print("=" * 50)
    
    print("\n1. اختبار حساب ديون العملاء:")
    test_customer_debt()
    
    print("\n2. اختبار حسابات الفواتير:")
    test_invoice_calculations()
    
    print("\n3. اختبار العلاقات:")
    test_relationships()
    
    print("\n" + "=" * 50)
    print("انتهى الاختبار")
    print("=" * 50)
