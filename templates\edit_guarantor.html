{% extends "base.html" %}

{% block title %}تعديل بيانات الضامن - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-user-edit text-primary"></i>
                تعديل بيانات الضامن
            </h1>
            <a href="{{ url_for('guarantors') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right"></i>
                العودة للضامنين
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-shield"></i>
                    تعديل بيانات: {{ guarantor.name }}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- الاسم الكامل -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label required">الاسم الكامل</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ guarantor.name }}" required>
                            <div class="invalid-feedback">
                                يرجى إدخال الاسم الكامل
                            </div>
                        </div>
                        
                        <!-- رقم الهاتف -->
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label required">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="{{ guarantor.phone }}" placeholder="05xxxxxxxx" required>
                            <div class="invalid-feedback">
                                يرجى إدخال رقم هاتف صحيح
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- رقم الهوية -->
                        <div class="col-md-6 mb-3">
                            <label for="national_id" class="form-label">رقم الهوية الوطنية</label>
                            <input type="text" class="form-control" id="national_id" name="national_id" 
                                   value="{{ guarantor.national_id or '' }}" placeholder="1234567890" maxlength="10">
                            <div class="form-text">اختياري - 10 أرقام</div>
                        </div>
                        
                        <!-- العلاقة -->
                        <div class="col-md-6 mb-3">
                            <label for="relationship" class="form-label">العلاقة بالعميل</label>
                            <select class="form-select" id="relationship" name="relationship">
                                <option value="">اختر العلاقة</option>
                                <option value="والد" {% if guarantor.relationship == 'والد' %}selected{% endif %}>والد</option>
                                <option value="أخ" {% if guarantor.relationship == 'أخ' %}selected{% endif %}>أخ</option>
                                <option value="عم" {% if guarantor.relationship == 'عم' %}selected{% endif %}>عم</option>
                                <option value="خال" {% if guarantor.relationship == 'خال' %}selected{% endif %}>خال</option>
                                <option value="صديق" {% if guarantor.relationship == 'صديق' %}selected{% endif %}>صديق</option>
                                <option value="زميل" {% if guarantor.relationship == 'زميل' %}selected{% endif %}>زميل</option>
                                <option value="أخرى" {% if guarantor.relationship == 'أخرى' %}selected{% endif %}>أخرى</option>
                            </select>
                            <div class="form-text">اختياري</div>
                        </div>
                    </div>
                    
                    <!-- العنوان -->
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3" 
                                  placeholder="المدينة - الحي - الشارع">{{ guarantor.address or '' }}</textarea>
                        <div class="form-text">اختياري</div>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="d-flex gap-2 justify-content-end">
                        <a href="{{ url_for('guarantors') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ التعديلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات الضامن -->
<div class="row justify-content-center mt-4">
    <div class="col-lg-8">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i>
                    معلومات الضامن
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>تاريخ التسجيل:</strong> {{ guarantor.created_at.strftime('%Y-%m-%d') }}</p>
                        <p><strong>رقم الضامن:</strong> {{ guarantor.id }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>عدد العملاء المضمونين:</strong> {{ guarantor.invoices|length }}</p>
                        <p><strong>العلاقة:</strong> 
                            {% if guarantor.relationship %}
                                <span class="badge bg-info">{{ guarantor.relationship }}</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
                
                {% if guarantor.invoices %}
                <hr>
                <h6><i class="fas fa-users"></i> العملاء المضمونين:</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>اسم العميل</th>
                                <th>رقم الفاتورة</th>
                                <th>المبلغ الإجمالي</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in guarantor.invoices %}
                            <tr>
                                <td>{{ invoice.customer.name }}</td>
                                <td>{{ invoice.invoice_number }}</td>
                                <td>{{ "{:,.0f}".format(invoice.total_amount) }} ر.س</td>
                                <td>
                                    {% if invoice.status == 'نشط' %}
                                    <span class="badge bg-success">{{ invoice.status }}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ invoice.status }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من رقم الهاتف
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, ''); // إزالة كل شيء عدا الأرقام
        
        if (value.length > 0 && !value.startsWith('05')) {
            if (value.startsWith('5')) {
                value = '0' + value;
            } else if (!value.startsWith('0')) {
                value = '05' + value;
            }
        }
        
        if (value.length > 10) {
            value = value.substring(0, 10);
        }
        
        this.value = value;
    });
    
    // التحقق من رقم الهوية
    const nationalIdInput = document.getElementById('national_id');
    nationalIdInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, ''); // إزالة كل شيء عدا الأرقام
        
        if (value.length > 10) {
            value = value.substring(0, 10);
        }
        
        this.value = value;
    });
    
    // تنسيق الاسم
    const nameInput = document.getElementById('name');
    nameInput.addEventListener('blur', function() {
        // تنظيف المسافات الزائدة
        this.value = this.value.trim().replace(/\s+/g, ' ');
    });
});
</script>
{% endblock %}
