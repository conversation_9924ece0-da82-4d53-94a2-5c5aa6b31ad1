{% extends "base.html" %}

{% block title %}إضافة دراجة نارية جديدة - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-motorcycle text-primary"></i>
                إضافة دراجة نارية جديدة
            </h1>
            <a href="{{ url_for('motorcycles') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right"></i>
                العودة للدراجات
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-motorcycle"></i>
                    بيانات الدراجة النارية
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- الماركة -->
                        <div class="col-md-6 mb-3">
                            <label for="brand" class="form-label required">الماركة</label>
                            <select class="form-select" id="brand" name="brand" required>
                                <option value="">اختر الماركة</option>
                                <option value="هوندا">هوندا (Honda)</option>
                                <option value="ياماها">ياماها (Yamaha)</option>
                                <option value="سوزوكي">سوزوكي (Suzuki)</option>
                                <option value="كاواساكي">كاواساكي (Kawasaki)</option>
                                <option value="دوكاتي">دوكاتي (Ducati)</option>
                                <option value="بي إم دبليو">بي إم دبليو (BMW)</option>
                                <option value="هارلي ديفيدسون">هارلي ديفيدسون (Harley-Davidson)</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار الماركة
                            </div>
                        </div>
                        
                        <!-- الموديل -->
                        <div class="col-md-6 mb-3">
                            <label for="model" class="form-label required">الموديل</label>
                            <input type="text" class="form-control" id="model" name="model" 
                                   placeholder="مثال: CBR 150" required>
                            <div class="invalid-feedback">
                                يرجى إدخال الموديل
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- سنة الصنع -->
                        <div class="col-md-4 mb-3">
                            <label for="year" class="form-label">سنة الصنع</label>
                            <select class="form-select" id="year" name="year">
                                <option value="">اختر السنة</option>
                                {% for year in range(2024, 2019, -1) %}
                                <option value="{{ year }}" {% if year == 2024 %}selected{% endif %}>{{ year }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <!-- حجم المحرك -->
                        <div class="col-md-4 mb-3">
                            <label for="engine_size" class="form-label">حجم المحرك</label>
                            <select class="form-select" id="engine_size" name="engine_size">
                                <option value="">اختر حجم المحرك</option>
                                <option value="125cc">125cc</option>
                                <option value="150cc">150cc</option>
                                <option value="200cc">200cc</option>
                                <option value="250cc">250cc</option>
                                <option value="300cc">300cc</option>
                                <option value="400cc">400cc</option>
                                <option value="500cc">500cc</option>
                                <option value="600cc">600cc</option>
                                <option value="750cc">750cc</option>
                                <option value="1000cc">1000cc</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        
                        <!-- اللون -->
                        <div class="col-md-4 mb-3">
                            <label for="color" class="form-label">اللون</label>
                            <select class="form-select" id="color" name="color">
                                <option value="">اختر اللون</option>
                                <option value="أحمر">أحمر</option>
                                <option value="أزرق">أزرق</option>
                                <option value="أسود">أسود</option>
                                <option value="أبيض">أبيض</option>
                                <option value="أخضر">أخضر</option>
                                <option value="أصفر">أصفر</option>
                                <option value="فضي">فضي</option>
                                <option value="رمادي">رمادي</option>
                                <option value="برتقالي">برتقالي</option>
                                <option value="بنفسجي">بنفسجي</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- السعر والعملة -->
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="price" class="form-label required">السعر</label>
                            <input type="number" class="form-control" id="price" name="price"
                                   placeholder="15000" min="1" step="0.01" required>
                            <div class="invalid-feedback">
                                يرجى إدخال السعر
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="currency_id" class="form-label required">العملة</label>
                            <select class="form-select" id="currency_id" name="currency_id" required>
                                {% for currency in currencies %}
                                <option value="{{ currency.id }}"
                                        {% if currency.is_base %}selected{% endif %}>
                                    {{ currency.name }} ({{ currency.symbol }})
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار العملة
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="d-flex gap-2 justify-content-end">
                        <a href="{{ url_for('motorcycles') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ الدراجة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row justify-content-center mt-4">
    <div class="col-lg-8">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i>
                    معلومات مهمة
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        السعر يجب أن يكون بالريال السعودي
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        يمكن إضافة ماركات وألوان جديدة حسب الحاجة
                    </li>
                    <li>
                        <i class="fas fa-check text-success me-2"></i>
                        يمكن تعديل بيانات الدراجة لاحقاً من قائمة الدراجات
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تنسيق السعر
    const priceInput = document.getElementById('price');
    priceInput.addEventListener('input', function() {
        // إزالة الأحرف غير الرقمية عدا النقطة
        let value = this.value.replace(/[^0-9.]/g, '');
        
        // التأكد من وجود نقطة واحدة فقط
        const parts = value.split('.');
        if (parts.length > 2) {
            value = parts[0] + '.' + parts.slice(1).join('');
        }
        
        this.value = value;
    });
    
    // إضافة خيار "أخرى" للماركة
    const brandSelect = document.getElementById('brand');
    brandSelect.addEventListener('change', function() {
        if (this.value === 'أخرى') {
            const customBrand = prompt('أدخل اسم الماركة:');
            if (customBrand && customBrand.trim()) {
                const option = new Option(customBrand.trim(), customBrand.trim());
                this.add(option);
                this.value = customBrand.trim();
            } else {
                this.value = '';
            }
        }
    });
    
    // إضافة خيار "أخرى" لحجم المحرك
    const engineSelect = document.getElementById('engine_size');
    engineSelect.addEventListener('change', function() {
        if (this.value === 'أخرى') {
            const customEngine = prompt('أدخل حجم المحرك:');
            if (customEngine && customEngine.trim()) {
                const option = new Option(customEngine.trim(), customEngine.trim());
                this.add(option);
                this.value = customEngine.trim();
            } else {
                this.value = '';
            }
        }
    });
});
</script>
{% endblock %}
