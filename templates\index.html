{% extends "base.html" %}

{% block title %}الصفحة الرئيسية - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt text-primary"></i>
            لوحة التحكم الرئيسية
        </h1>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_customers }}</h4>
                        <p class="card-text">إجمالي العملاء</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('customers') }}" class="text-white text-decoration-none">
                    عرض التفاصيل <i class="fas fa-arrow-left"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_invoices }}</h4>
                        <p class="card-text">إجمالي الفواتير</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-invoice fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('invoices') }}" class="text-white text-decoration-none">
                    عرض التفاصيل <i class="fas fa-arrow-left"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ "{:,.0f}".format(stats.total_sales) }} ر.س</h4>
                        <p class="card-text">إجمالي المبيعات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('reports') }}" class="text-white text-decoration-none">
                    عرض التقارير <i class="fas fa-arrow-left"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.overdue_installments }}</h4>
                        <p class="card-text">أقساط متأخرة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('reports') }}" class="text-white text-decoration-none">
                    عرض المتأخرات <i class="fas fa-arrow-left"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- الوصول السريع -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt text-warning"></i>
                    الوصول السريع
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ url_for('add_customer') }}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center">
                            <i class="fas fa-user-plus fa-2x mb-2"></i>
                            <span>إضافة عميل</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ url_for('add_guarantor') }}" class="btn btn-outline-success w-100 h-100 d-flex flex-column justify-content-center">
                            <i class="fas fa-user-shield fa-2x mb-2"></i>
                            <span>إضافة ضامن</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ url_for('add_motorcycle') }}" class="btn btn-outline-info w-100 h-100 d-flex flex-column justify-content-center">
                            <i class="fas fa-motorcycle fa-2x mb-2"></i>
                            <span>إضافة دراجة</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ url_for('add_invoice') }}" class="btn btn-outline-warning w-100 h-100 d-flex flex-column justify-content-center">
                            <i class="fas fa-file-invoice fa-2x mb-2"></i>
                            <span>فاتورة جديدة</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ url_for('receipts') }}" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column justify-content-center">
                            <i class="fas fa-receipt fa-2x mb-2"></i>
                            <span>سند قبض</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ url_for('reports') }}" class="btn btn-outline-dark w-100 h-100 d-flex flex-column justify-content-center">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i>
                            <span>التقارير</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الرسم البياني -->
<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line text-success"></i>
                    مبيعات آخر 6 أشهر
                </h5>
            </div>
            <div class="card-body">
                <canvas id="salesChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tasks text-info"></i>
                    الفواتير النشطة
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <h2 class="text-primary">{{ stats.active_invoices }}</h2>
                    <p class="text-muted">فاتورة نشطة</p>
                    <a href="{{ url_for('invoices') }}" class="btn btn-primary">
                        عرض الفواتير
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// رسم بياني للمبيعات
fetch('/api/sales_chart')
    .then(response => response.json())
    .then(data => {
        const ctx = document.getElementById('salesChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.map(item => item.month),
                datasets: [{
                    label: 'المبيعات (ر.س)',
                    data: data.map(item => item.sales),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: true
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString() + ' ر.س';
                            }
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}
