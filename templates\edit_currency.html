{% extends "base.html" %}

{% block title %}تعديل العملة {{ currency.name }} - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-edit text-primary"></i>
                تعديل العملة: {{ currency.name }}
            </h1>
            <div class="btn-group">
                <a href="{{ url_for('currencies') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة للعملات
                </a>
                <a href="{{ url_for('add_currency') }}" class="btn btn-outline-info">
                    <i class="fas fa-plus"></i>
                    إضافة عملة جديدة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- معلومات العملة الحالية -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i>
                    معلومات العملة الحالية
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>اسم العملة:</strong>
                        <p class="mb-0">{{ currency.name }}</p>
                    </div>
                    <div class="col-md-2">
                        <strong>الرمز:</strong>
                        <p class="mb-0"><span class="badge bg-primary">{{ currency.symbol }}</span></p>
                    </div>
                    <div class="col-md-2">
                        <strong>الكود:</strong>
                        <p class="mb-0"><code>{{ currency.code }}</code></p>
                    </div>
                    <div class="col-md-2">
                        <strong>سعر الصرف:</strong>
                        <p class="mb-0">{{ "{:.2f}".format(currency.exchange_rate) }}</p>
                    </div>
                    <div class="col-md-3">
                        <strong>الحالة:</strong>
                        <p class="mb-0">
                            {% if currency.is_base %}
                            <span class="badge bg-warning">عملة أساسية</span>
                            {% else %}
                            <span class="badge bg-info">عملة أجنبية</span>
                            {% endif %}
                            
                            {% if currency.is_active %}
                            <span class="badge bg-success">نشطة</span>
                            {% else %}
                            <span class="badge bg-secondary">معطلة</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit"></i>
                    تعديل بيانات العملة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- اسم العملة -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label required">اسم العملة</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ currency.name }}" required>
                            <div class="invalid-feedback">
                                يرجى إدخال اسم العملة
                            </div>
                        </div>
                        
                        <!-- رمز العملة -->
                        <div class="col-md-6 mb-3">
                            <label for="code" class="form-label required">رمز العملة (ISO)</label>
                            <input type="text" class="form-control" id="code" name="code" 
                                   value="{{ currency.code }}" maxlength="10" required style="text-transform: uppercase;">
                            <div class="invalid-feedback">
                                يرجى إدخال رمز العملة
                            </div>
                            <div class="form-text">رمز العملة الدولي (3 أحرف عادة)</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- رمز العملة للعرض -->
                        <div class="col-md-6 mb-3">
                            <label for="symbol" class="form-label required">رمز العرض</label>
                            <input type="text" class="form-control" id="symbol" name="symbol" 
                                   value="{{ currency.symbol }}" maxlength="10" required>
                            <div class="invalid-feedback">
                                يرجى إدخال رمز العرض
                            </div>
                            <div class="form-text">الرمز الذي سيظهر مع المبالغ</div>
                        </div>
                        
                        <!-- سعر الصرف -->
                        <div class="col-md-6 mb-3">
                            <label for="exchange_rate" class="form-label required">سعر الصرف</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="exchange_rate" name="exchange_rate" 
                                       value="{{ currency.exchange_rate }}" min="0.01" step="0.01" required
                                       {% if currency.is_base %}readonly class="bg-light"{% endif %}>
                                <span class="input-group-text">مقابل العملة الأساسية</span>
                            </div>
                            <div class="invalid-feedback">
                                يرجى إدخال سعر الصرف
                            </div>
                            {% if currency.is_base %}
                            <div class="form-text text-warning">
                                <i class="fas fa-star"></i>
                                سعر صرف العملة الأساسية دائماً 1.00
                            </div>
                            {% else %}
                            <div class="form-text">كم من العملة الأساسية يساوي وحدة واحدة من هذه العملة</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- خيارات العملة -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_base" name="is_base"
                                       {% if currency.is_base %}checked{% endif %}>
                                <label class="form-check-label" for="is_base">
                                    <i class="fas fa-star text-warning"></i>
                                    عملة أساسية
                                </label>
                                <div class="form-text">إذا كانت هذه العملة الأساسية للنظام (سيتم إلغاء العملة الأساسية السابقة)</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                       {% if currency.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">
                                    <i class="fas fa-check-circle text-success"></i>
                                    عملة نشطة
                                </label>
                                <div class="form-text">هل ستكون هذه العملة متاحة للاستخدام</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="d-flex gap-2 justify-content-end">
                        <a href="{{ url_for('currencies') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save"></i>
                            حفظ التعديلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- تحذيرات مهمة -->
<div class="row justify-content-center mt-4">
    <div class="col-lg-8">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h6 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle"></i>
                    تحذيرات مهمة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-star text-warning"></i> العملة الأساسية:</h6>
                        <ul class="list-unstyled">
                            <li>• تغيير العملة الأساسية سيؤثر على جميع الحسابات</li>
                            <li>• سعر صرف العملة الأساسية دائماً 1.00</li>
                            <li>• يمكن أن تكون هناك عملة أساسية واحدة فقط</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-exchange-alt text-info"></i> سعر الصرف:</h6>
                        <ul class="list-unstyled">
                            <li>• تغيير سعر الصرف لن يؤثر على المعاملات السابقة</li>
                            <li>• المعاملات الجديدة ستستخدم السعر الجديد</li>
                            <li>• تأكد من دقة السعر قبل الحفظ</li>
                        </ul>
                    </div>
                </div>
                
                {% if currency.is_base %}
                <div class="alert alert-warning mt-3 mb-0">
                    <i class="fas fa-star"></i>
                    <strong>تنبيه:</strong> هذه هي العملة الأساسية للنظام. إلغاء تفعيلها أو تغييرها قد يؤثر على جميع المعاملات.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديث سعر الصرف عند تحديد العملة كأساسية
    const isBaseCheckbox = document.getElementById('is_base');
    const exchangeRateInput = document.getElementById('exchange_rate');
    
    isBaseCheckbox.addEventListener('change', function() {
        if (this.checked) {
            exchangeRateInput.value = '1.00';
            exchangeRateInput.readOnly = true;
            exchangeRateInput.classList.add('bg-light');
        } else {
            exchangeRateInput.readOnly = false;
            exchangeRateInput.classList.remove('bg-light');
        }
    });
    
    // تحويل رمز العملة إلى أحرف كبيرة
    document.getElementById('code').addEventListener('input', function() {
        this.value = this.value.toUpperCase();
    });
    
    // تأكيد التعديل للعملة الأساسية
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const isBase = document.getElementById('is_base').checked;
        const wasBase = {{ 'true' if currency.is_base else 'false' }};
        
        if (wasBase && !isBase) {
            if (!confirm('هل أنت متأكد من إلغاء كون هذه العملة أساسية؟ هذا قد يؤثر على النظام.')) {
                e.preventDefault();
                return false;
            }
        }
        
        if (!wasBase && isBase) {
            if (!confirm('هل أنت متأكد من جعل هذه العملة أساسية؟ سيتم إلغاء العملة الأساسية الحالية.')) {
                e.preventDefault();
                return false;
            }
        }
    });
});
</script>
{% endblock %}
