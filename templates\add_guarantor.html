{% extends "base.html" %}

{% block title %}إضافة ضامن جديد - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-user-shield text-primary"></i>
                إضافة ضامن جديد
            </h1>
            <a href="{{ url_for('guarantors') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right"></i>
                العودة للضامنين
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-shield"></i>
                    بيانات الضامن
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- الاسم الكامل -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label required">الاسم الكامل</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="invalid-feedback">
                                يرجى إدخال الاسم الكامل
                            </div>
                        </div>
                        
                        <!-- رقم الهاتف -->
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label required">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   placeholder="05xxxxxxxx" required>
                            <div class="invalid-feedback">
                                يرجى إدخال رقم هاتف صحيح
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- رقم الهوية -->
                        <div class="col-md-6 mb-3">
                            <label for="national_id" class="form-label">رقم الهوية الوطنية</label>
                            <input type="text" class="form-control" id="national_id" name="national_id" 
                                   placeholder="1234567890" maxlength="10">
                            <div class="form-text">اختياري - 10 أرقام</div>
                        </div>
                        
                        <!-- العلاقة -->
                        <div class="col-md-6 mb-3">
                            <label for="relationship" class="form-label">العلاقة بالعميل</label>
                            <select class="form-select" id="relationship" name="relationship">
                                <option value="">اختر العلاقة</option>
                                <option value="والد">والد</option>
                                <option value="أخ">أخ</option>
                                <option value="عم">عم</option>
                                <option value="خال">خال</option>
                                <option value="صديق">صديق</option>
                                <option value="زميل">زميل</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                            <div class="form-text">اختياري</div>
                        </div>
                    </div>
                    
                    <!-- العنوان -->
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3" 
                                  placeholder="المدينة - الحي - الشارع"></textarea>
                        <div class="form-text">اختياري</div>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="d-flex gap-2 justify-content-end">
                        <a href="{{ url_for('guarantors') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ الضامن
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row justify-content-center mt-4">
    <div class="col-lg-8">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i>
                    معلومات مهمة
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        رقم الهاتف يجب أن يبدأ بـ 05 ويتكون من 10 أرقام
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        رقم الهوية الوطنية يجب أن يتكون من 10 أرقام
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        العلاقة بالعميل تساعد في تنظيم البيانات
                    </li>
                    <li>
                        <i class="fas fa-check text-success me-2"></i>
                        يمكن تعديل بيانات الضامن لاحقاً من قائمة الضامنين
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من رقم الهاتف
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, ''); // إزالة كل شيء عدا الأرقام
        
        if (value.length > 0 && !value.startsWith('05')) {
            if (value.startsWith('5')) {
                value = '0' + value;
            } else if (!value.startsWith('0')) {
                value = '05' + value;
            }
        }
        
        if (value.length > 10) {
            value = value.substring(0, 10);
        }
        
        this.value = value;
    });
    
    // التحقق من رقم الهوية
    const nationalIdInput = document.getElementById('national_id');
    nationalIdInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, ''); // إزالة كل شيء عدا الأرقام
        
        if (value.length > 10) {
            value = value.substring(0, 10);
        }
        
        this.value = value;
    });
    
    // تنسيق الاسم
    const nameInput = document.getElementById('name');
    nameInput.addEventListener('blur', function() {
        // تنظيف المسافات الزائدة
        this.value = this.value.trim().replace(/\s+/g, ' ');
    });
});
</script>
{% endblock %}
