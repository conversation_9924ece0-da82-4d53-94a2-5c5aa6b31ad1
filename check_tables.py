#!/usr/bin/env python3
"""
فحص أسماء الجداول في قاعدة البيانات
"""

import sqlite3
import os

def check_database_structure():
    """فحص بنية قاعدة البيانات"""
    
    if not os.path.exists('motorcycle_sales.db'):
        print("قاعدة البيانات غير موجودة!")
        return
    
    conn = sqlite3.connect('motorcycle_sales.db')
    cursor = conn.cursor()
    
    try:
        # الحصول على قائمة الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print("الجداول الموجودة في قاعدة البيانات:")
        print("-" * 40)
        
        for table in tables:
            table_name = table[0]
            print(f"\nجدول: {table_name}")
            
            # الحصول على أعمدة الجدول
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            print("الأعمدة:")
            for column in columns:
                col_id, col_name, col_type, not_null, default_val, pk = column
                print(f"  - {col_name} ({col_type})")
        
    except Exception as e:
        print(f"خطأ: {e}")
        
    finally:
        conn.close()

if __name__ == '__main__':
    check_database_structure()
