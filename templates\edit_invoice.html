{% extends "base.html" %}

{% block title %}تعديل الفاتورة {{ invoice.invoice_number }} - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-edit text-primary"></i>
                تعديل الفاتورة {{ invoice.invoice_number }}
            </h1>
            <div class="btn-group">
                <a href="{{ url_for('view_invoice', id=invoice.id) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة للفاتورة
                </a>
                <a href="{{ url_for('invoices') }}" class="btn btn-outline-info">
                    <i class="fas fa-list"></i>
                    قائمة الفواتير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- تحذير -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>تنبيه:</strong> تعديل الفاتورة قد يؤثر على الأقساط المجدولة. الأقساط غير المدفوعة سيتم إعادة حسابها.
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-invoice"></i>
                    تعديل بيانات الفاتورة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- العميل -->
                        <div class="col-md-6 mb-3">
                            <label for="customer_id" class="form-label required">العميل</label>
                            <select class="form-select" id="customer_id" name="customer_id" required>
                                <option value="">اختر العميل</option>
                                {% for customer in customers %}
                                <option value="{{ customer.id }}" {% if customer.id == invoice.customer_id %}selected{% endif %}>
                                    {{ customer.name }} - {{ customer.phone }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار العميل
                            </div>
                        </div>
                        
                        <!-- الضامن -->
                        <div class="col-md-6 mb-3">
                            <label for="guarantor_id" class="form-label required">الضامن</label>
                            <select class="form-select" id="guarantor_id" name="guarantor_id" required>
                                <option value="">اختر الضامن</option>
                                {% for guarantor in guarantors %}
                                <option value="{{ guarantor.id }}" {% if guarantor.id == invoice.guarantor_id %}selected{% endif %}>
                                    {{ guarantor.name }} - {{ guarantor.phone }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار الضامن
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- الدراجة النارية -->
                        <div class="col-md-6 mb-3">
                            <label for="motorcycle_id" class="form-label required">الدراجة النارية</label>
                            <select class="form-select" id="motorcycle_id" name="motorcycle_id" required>
                                <option value="">اختر الدراجة</option>
                                {% for motorcycle in motorcycles %}
                                <option value="{{ motorcycle.id }}"
                                        {% if motorcycle.id == invoice.motorcycle_id %}selected{% endif %}>
                                    {{ motorcycle.brand }} {{ motorcycle.model }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار الدراجة النارية
                            </div>
                        </div>
                        
                        <!-- المبلغ الإجمالي -->
                        <div class="col-md-6 mb-3">
                            <label for="total_amount" class="form-label required">المبلغ الإجمالي</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="total_amount" name="total_amount" 
                                       value="{{ invoice.total_amount }}" min="1" step="0.01" required>
                                <span class="input-group-text">ر.س</span>
                            </div>
                            <div class="invalid-feedback">
                                يرجى إدخال المبلغ الإجمالي
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- الدفعة المقدمة -->
                        <div class="col-md-4 mb-3">
                            <label for="down_payment" class="form-label">الدفعة المقدمة</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="down_payment" name="down_payment" 
                                       value="{{ invoice.down_payment }}" min="0" step="0.01">
                                <span class="input-group-text">ر.س</span>
                            </div>
                        </div>
                        
                        <!-- عدد الأقساط -->
                        <div class="col-md-4 mb-3">
                            <label for="installments_count" class="form-label required">عدد الأقساط</label>
                            <select class="form-select" id="installments_count" name="installments_count" required>
                                <option value="">اختر عدد الأقساط</option>
                                <option value="6" {% if invoice.installments_count == 6 %}selected{% endif %}>6 أقساط</option>
                                <option value="12" {% if invoice.installments_count == 12 %}selected{% endif %}>12 قسط</option>
                                <option value="18" {% if invoice.installments_count == 18 %}selected{% endif %}>18 قسط</option>
                                <option value="24" {% if invoice.installments_count == 24 %}selected{% endif %}>24 قسط</option>
                                <option value="36" {% if invoice.installments_count == 36 %}selected{% endif %}>36 قسط</option>
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار عدد الأقساط
                            </div>
                        </div>
                        
                        <!-- تاريخ أول قسط -->
                        <div class="col-md-4 mb-3">
                            <label for="first_installment_date" class="form-label required">تاريخ أول قسط</label>
                            <input type="date" class="form-control" id="first_installment_date" name="first_installment_date" 
                                   value="{{ invoice.first_installment_date.strftime('%Y-%m-%d') }}" required>
                            <div class="invalid-feedback">
                                يرجى اختيار تاريخ أول قسط
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- المبلغ المتبقي -->
                        <div class="col-md-6 mb-3">
                            <label for="remaining_amount" class="form-label">المبلغ المتبقي</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="remaining_amount" name="remaining_amount" 
                                       value="{{ invoice.remaining_amount }}" readonly>
                                <span class="input-group-text">ر.س</span>
                            </div>
                        </div>
                        
                        <!-- قيمة القسط -->
                        <div class="col-md-6 mb-3">
                            <label for="installment_amount" class="form-label">قيمة القسط الواحد</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="installment_amount" name="installment_amount" 
                                       value="{{ invoice.installment_amount }}" readonly>
                                <span class="input-group-text">ر.س</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- ملاحظات -->
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="أي ملاحظات إضافية...">{{ invoice.notes or '' }}</textarea>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="d-flex gap-2 justify-content-end">
                        <a href="{{ url_for('view_invoice', id=invoice.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ التعديلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات الأقساط الحالية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i>
                    حالة الأقساط الحالية
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h5 class="text-success">{{ invoice.installments|selectattr('is_paid')|list|length }}</h5>
                            <p class="mb-0">أقساط مدفوعة</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h5 class="text-warning">{{ invoice.installments|rejectattr('is_paid')|rejectattr('is_overdue')|list|length }}</h5>
                            <p class="mb-0">أقساط مستحقة</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h5 class="text-danger">{{ invoice.installments|selectattr('is_overdue')|list|length }}</h5>
                            <p class="mb-0">أقساط متأخرة</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h5 class="text-primary">{{ "{:,.0f}".format(invoice.get_paid_amount()) }} ر.س</h5>
                            <p class="mb-0">إجمالي المدفوع</p>
                        </div>
                    </div>
                </div>
                
                <hr>
                <div class="alert alert-warning mb-0">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>ملاحظة:</strong> عند تعديل المبالغ أو عدد الأقساط، سيتم إعادة حساب الأقساط غير المدفوعة فقط. الأقساط المدفوعة ستبقى كما هي.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // إزالة التحديث التلقائي للسعر
    const motorcycleSelect = document.getElementById('motorcycle_id');

    motorcycleSelect.addEventListener('change', function() {
        // يمكن إضافة منطق آخر هنا إذا لزم الأمر
        calculateInstallments();
    });
    
    // تحديث الحسابات عند تغيير الدفعة المقدمة أو عدد الأقساط
    document.getElementById('down_payment').addEventListener('input', calculateInstallments);
    document.getElementById('installments_count').addEventListener('change', calculateInstallments);
    document.getElementById('total_amount').addEventListener('input', calculateInstallments);
    
    function calculateInstallments() {
        const totalAmount = parseFloat(document.getElementById('total_amount').value) || 0;
        const downPayment = parseFloat(document.getElementById('down_payment').value) || 0;
        const installmentsCount = parseInt(document.getElementById('installments_count').value) || 1;
        
        if (totalAmount > 0) {
            const remainingAmount = totalAmount - downPayment;
            const installmentAmount = remainingAmount / installmentsCount;
            
            document.getElementById('remaining_amount').value = remainingAmount.toFixed(2);
            document.getElementById('installment_amount').value = installmentAmount.toFixed(2);
        }
    }
    
    // التحقق من صحة الدفعة المقدمة
    document.getElementById('down_payment').addEventListener('input', function() {
        const totalAmount = parseFloat(document.getElementById('total_amount').value) || 0;
        const downPayment = parseFloat(this.value) || 0;
        
        if (downPayment > totalAmount) {
            this.value = totalAmount;
            calculateInstallments();
        }
    });
    
    // حساب القيم الأولية
    calculateInstallments();
});
</script>
{% endblock %}
