#!/usr/bin/env python3
"""
إصلاح أعمدة العملة في قاعدة البيانات
"""

import sqlite3
import os
from datetime import datetime

def find_database():
    """البحث عن قاعدة البيانات"""
    db_paths = ['motorcycle_sales.db', 'instance/motorcycle_sales.db']
    
    for path in db_paths:
        if os.path.exists(path):
            print(f"تم العثور على قاعدة البيانات: {path}")
            return path
    
    print("لم يتم العثور على قاعدة البيانات!")
    return None

def get_table_info(cursor, table_name):
    """الحصول على معلومات الجدول"""
    try:
        cursor.execute(f"PRAGMA table_info({table_name})")
        return cursor.fetchall()
    except:
        return []

def check_column_exists(cursor, table_name, column_name):
    """التحقق من وجود عمود في جدول"""
    columns_info = get_table_info(cursor, table_name)
    columns = [column[1] for column in columns_info]
    return column_name in columns

def fix_currency_columns():
    """إصلاح أعمدة العملة"""
    
    # البحث عن قاعدة البيانات
    db_path = find_database()
    if not db_path:
        return False
    
    # إنشاء نسخة احتياطية
    backup_name = f'backup_currency_fix_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
    if os.name == 'nt':  # Windows
        os.system(f'copy "{db_path}" "{backup_name}"')
    else:  # Unix/Linux
        os.system(f'cp "{db_path}" "{backup_name}"')
    print(f"تم إنشاء نسخة احتياطية: {backup_name}")
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("بدء إصلاح أعمدة العملة...")
        
        # الحصول على قائمة الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [table[0] for table in cursor.fetchall()]
        print(f"الجداول الموجودة: {tables}")
        
        # 1. إنشاء جدول العملات إذا لم يكن موجوداً
        if 'currencies' not in tables:
            print("إنشاء جدول العملات...")
            cursor.execute('''
                CREATE TABLE currencies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(100) NOT NULL,
                    code VARCHAR(10) NOT NULL UNIQUE,
                    symbol VARCHAR(10) NOT NULL,
                    exchange_rate FLOAT NOT NULL DEFAULT 1.0,
                    is_base BOOLEAN DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إضافة العملات الافتراضية
            currencies = [
                ('الريال السعودي', 'SAR', 'ر.س', 1.0, 1, 1),
                ('الدولار الأمريكي', 'USD', '$', 3.75, 0, 1),
                ('اليورو', 'EUR', '€', 4.10, 0, 1),
                ('الجنيه الإسترليني', 'GBP', '£', 4.75, 0, 1),
                ('الدرهم الإماراتي', 'AED', 'د.إ', 1.02, 0, 1)
            ]
            
            cursor.executemany('''
                INSERT INTO currencies (name, code, symbol, exchange_rate, is_base, is_active)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', currencies)
            print("تم إضافة العملات الافتراضية")
        
        # 2. إصلاح الجداول التي تحتاج أعمدة العملة
        tables_to_fix = []
        
        # البحث عن الجداول المختلفة
        possible_table_names = {
            'motorcycles': ['motorcycles'],
            'invoices': ['invoices', 'bills'],  # قد يكون اسم الجدول bills
            'receipts': ['receipts'],
            'customers': ['customers'],
            'guarantors': ['guarantors']
        }
        
        actual_tables = {}
        for logical_name, possible_names in possible_table_names.items():
            for name in possible_names:
                if name in tables:
                    actual_tables[logical_name] = name
                    break
        
        print(f"الجداول الفعلية: {actual_tables}")
        
        # 3. إضافة أعمدة العملة للجداول التي تحتاجها
        tables_needing_currency = ['motorcycles', 'invoices', 'receipts']
        
        for logical_name in tables_needing_currency:
            if logical_name in actual_tables:
                table_name = actual_tables[logical_name]
                
                if not check_column_exists(cursor, table_name, 'currency_id'):
                    print(f"إضافة عمود العملة لجدول {table_name}...")
                    cursor.execute(f'ALTER TABLE {table_name} ADD COLUMN currency_id INTEGER DEFAULT 1')
                    cursor.execute(f'UPDATE {table_name} SET currency_id = 1 WHERE currency_id IS NULL')
                    print(f"تم إضافة عمود العملة لجدول {table_name}")
                else:
                    print(f"عمود العملة موجود بالفعل في جدول {table_name}")
        
        # 4. حفظ التغييرات
        conn.commit()
        print("تم حفظ جميع التغييرات بنجاح!")
        
        return True
        
    except Exception as e:
        print(f"خطأ في إصلاح أعمدة العملة: {e}")
        conn.rollback()
        return False
        
    finally:
        conn.close()

def verify_fix():
    """التحقق من الإصلاح"""
    db_path = find_database()
    if not db_path:
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("\nالتحقق من الإصلاح...")
        
        # فحص جدول العملات
        cursor.execute('SELECT COUNT(*) FROM currencies')
        currency_count = cursor.fetchone()[0]
        print(f"✓ جدول العملات: {currency_count} عملة")
        
        # فحص الجداول الأخرى
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [table[0] for table in cursor.fetchall()]
        
        tables_to_check = []
        for table in tables:
            if table in ['motorcycles', 'invoices', 'bills', 'receipts']:
                tables_to_check.append(table)
        
        for table in tables_to_check:
            if check_column_exists(cursor, table, 'currency_id'):
                print(f"✓ جدول {table}: يحتوي على عمود currency_id")
            else:
                print(f"✗ جدول {table}: لا يحتوي على عمود currency_id")
        
        return True
        
    except Exception as e:
        print(f"خطأ في التحقق: {e}")
        return False
        
    finally:
        conn.close()

if __name__ == '__main__':
    print("=" * 60)
    print("إصلاح أعمدة العملة في قاعدة البيانات")
    print("=" * 60)
    
    if fix_currency_columns():
        verify_fix()
        print("\n" + "=" * 60)
        print("تم إصلاح أعمدة العملة بنجاح!")
        print("يمكنك الآن تشغيل التطبيق")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("فشل في إصلاح أعمدة العملة!")
        print("=" * 60)
