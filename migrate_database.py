#!/usr/bin/env python3
"""
سكريبت تحديث قاعدة البيانات لإضافة دعم العملات
"""

import sqlite3
import os
from datetime import datetime

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    db_paths = ['motorcycle_sales.db', 'instance/motorcycle_sales.db']

    for db_path in db_paths:
        if os.path.exists(db_path):
            backup_name = f'{db_path.replace("/", "_").replace(".db", "")}_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
            if os.name == 'nt':  # Windows
                os.system(f'copy "{db_path}" "{backup_name}"')
            else:  # Unix/Linux
                os.system(f'cp "{db_path}" "{backup_name}"')
            print(f"تم إنشاء نسخة احتياطية: {backup_name}")
            return db_path
    return None

def migrate_database():
    """تحديث قاعدة البيانات لإضافة دعم العملات"""

    # إنشاء نسخة احتياطية والحصول على مسار قاعدة البيانات
    db_path = backup_database()
    if not db_path:
        print("لم يتم العثور على قاعدة البيانات!")
        return False

    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("بدء تحديث قاعدة البيانات...")
        
        # 1. إنشاء جدول العملات
        print("إنشاء جدول العملات...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS currencies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                code VARCHAR(10) NOT NULL UNIQUE,
                symbol VARCHAR(10) NOT NULL,
                exchange_rate FLOAT NOT NULL DEFAULT 1.0,
                is_base BOOLEAN DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 2. إضافة العملات الافتراضية
        print("إضافة العملات الافتراضية...")
        currencies = [
            ('الريال السعودي', 'SAR', 'ر.س', 1.0, 1, 1),
            ('الدولار الأمريكي', 'USD', '$', 3.75, 0, 1),
            ('اليورو', 'EUR', '€', 4.10, 0, 1),
            ('الجنيه الإسترليني', 'GBP', '£', 4.75, 0, 1),
            ('الدرهم الإماراتي', 'AED', 'د.إ', 1.02, 0, 1)
        ]
        
        cursor.execute('SELECT COUNT(*) FROM currencies')
        if cursor.fetchone()[0] == 0:
            cursor.executemany('''
                INSERT INTO currencies (name, code, symbol, exchange_rate, is_base, is_active)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', currencies)
            print("تم إضافة العملات الافتراضية")
        
        # 3. التحقق من وجود الأعمدة وإضافتها إذا لم تكن موجودة
        
        # فحص جدول الدراجات
        cursor.execute("PRAGMA table_info(motorcycles)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'currency_id' not in columns:
            print("إضافة عمود العملة لجدول الدراجات...")
            cursor.execute('ALTER TABLE motorcycles ADD COLUMN currency_id INTEGER DEFAULT 1')
            cursor.execute('UPDATE motorcycles SET currency_id = 1 WHERE currency_id IS NULL')
        
        # فحص جدول الفواتير
        cursor.execute("PRAGMA table_info(invoices)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'currency_id' not in columns:
            print("إضافة عمود العملة لجدول الفواتير...")
            cursor.execute('ALTER TABLE invoices ADD COLUMN currency_id INTEGER DEFAULT 1')
            cursor.execute('UPDATE invoices SET currency_id = 1 WHERE currency_id IS NULL')
        
        # فحص جدول سندات القبض
        cursor.execute("PRAGMA table_info(receipts)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'currency_id' not in columns:
            print("إضافة عمود العملة لجدول سندات القبض...")
            cursor.execute('ALTER TABLE receipts ADD COLUMN currency_id INTEGER DEFAULT 1')
            cursor.execute('UPDATE receipts SET currency_id = 1 WHERE currency_id IS NULL')
        
        # 4. حفظ التغييرات
        conn.commit()
        print("تم تحديث قاعدة البيانات بنجاح!")
        
        # 5. التحقق من النتائج
        cursor.execute('SELECT COUNT(*) FROM currencies')
        currency_count = cursor.fetchone()[0]
        print(f"عدد العملات في النظام: {currency_count}")
        
        cursor.execute('SELECT name, code, symbol FROM currencies WHERE is_base = 1')
        base_currency = cursor.fetchone()
        if base_currency:
            print(f"العملة الأساسية: {base_currency[0]} ({base_currency[1]}) - {base_currency[2]}")
        
        return True
        
    except Exception as e:
        print(f"خطأ في تحديث قاعدة البيانات: {e}")
        conn.rollback()
        return False
        
    finally:
        conn.close()

def verify_migration():
    """التحقق من نجاح التحديث"""
    # البحث عن قاعدة البيانات
    db_paths = ['motorcycle_sales.db', 'instance/motorcycle_sales.db']
    db_path = None

    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            break

    if not db_path:
        print("لم يتم العثور على قاعدة البيانات!")
        return False

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("\nالتحقق من التحديث...")
        
        # فحص جدول العملات
        cursor.execute('SELECT COUNT(*) FROM currencies')
        currency_count = cursor.fetchone()[0]
        print(f"✓ جدول العملات: {currency_count} عملة")
        
        # فحص أعمدة العملة في الجداول الأخرى
        tables_to_check = ['motorcycles', 'invoices', 'receipts']
        
        for table in tables_to_check:
            cursor.execute(f"PRAGMA table_info({table})")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'currency_id' in columns:
                print(f"✓ جدول {table}: يحتوي على عمود currency_id")
            else:
                print(f"✗ جدول {table}: لا يحتوي على عمود currency_id")
        
        return True
        
    except Exception as e:
        print(f"خطأ في التحقق: {e}")
        return False
        
    finally:
        conn.close()

if __name__ == '__main__':
    print("=" * 50)
    print("سكريبت تحديث قاعدة البيانات لدعم العملات")
    print("=" * 50)
    
    # تحديث قاعدة البيانات
    if migrate_database():
        # التحقق من النتائج
        verify_migration()
        print("\n" + "=" * 50)
        print("تم تحديث قاعدة البيانات بنجاح!")
        print("يمكنك الآن تشغيل التطبيق بأمان")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("فشل في تحديث قاعدة البيانات!")
        print("تحقق من الأخطاء أعلاه")
        print("=" * 50)
