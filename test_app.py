from flask import Flask, render_template
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'test-key'

@app.route('/')
def index():
    stats = {
        'total_customers': 5,
        'total_invoices': 10,
        'total_sales': 150000,
        'overdue_installments': 3,
        'active_invoices': 8
    }
    return render_template('index.html', stats=stats)

@app.route('/customers')
def customers():
    return "<h1>صفحة العملاء</h1>"

if __name__ == '__main__':
    print("Starting Flask app...")
    app.run(debug=True, host='0.0.0.0', port=5000)
