{% extends "base.html" %}

{% block title %}إدارة الضامنين - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-user-shield text-primary"></i>
                إدارة الضامنين
            </h1>
            <a href="{{ url_for('add_guarantor') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة ضامن جديد
            </a>
        </div>
    </div>
</div>

<!-- شريط البحث -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-8">
                        <input type="text" class="form-control" name="search" 
                               placeholder="البحث بالاسم أو رقم الهاتف أو رقم الهوية..." 
                               value="{{ search }}">
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-outline-primary w-100">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول الضامنين -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i>
                    قائمة الضامنين ({{ guarantors.total }} ضامن)
                </h5>
            </div>
            <div class="card-body">
                {% if guarantors.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الاسم</th>
                                <th>رقم الهاتف</th>
                                <th>رقم الهوية</th>
                                <th>العلاقة</th>
                                <th>عدد العملاء المضمونين</th>
                                <th>تاريخ التسجيل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for guarantor in guarantors.items %}
                            <tr>
                                <td>{{ guarantor.id }}</td>
                                <td>
                                    <strong>{{ guarantor.name }}</strong>
                                    {% if guarantor.address %}
                                    <br><small class="text-muted">{{ guarantor.address }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="tel:{{ guarantor.phone }}" class="text-decoration-none">
                                        {{ guarantor.phone }}
                                    </a>
                                </td>
                                <td>{{ guarantor.national_id or '-' }}</td>
                                <td>
                                    {% if guarantor.relationship %}
                                    <span class="badge bg-info">{{ guarantor.relationship }}</span>
                                    {% else %}
                                    -
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ guarantor.invoices|length }}</span>
                                </td>
                                <td>{{ guarantor.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="#" 
                                           class="btn btn-sm btn-outline-primary" 
                                           data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" 
                                           class="btn btn-sm btn-outline-info" 
                                           data-bs-toggle="tooltip" title="كشف حساب">
                                            <i class="fas fa-file-alt"></i>
                                        </a>
                                        <a href="#" 
                                           class="btn btn-sm btn-outline-danger" 
                                           data-bs-toggle="tooltip" title="حذف"
                                           onclick="return confirmDelete('هل أنت متأكد من حذف هذا الضامن؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- الصفحات -->
                {% if guarantors.pages > 1 %}
                <nav aria-label="صفحات الضامنين">
                    <ul class="pagination justify-content-center">
                        {% if guarantors.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('guarantors', page=guarantors.prev_num, search=search) }}">
                                السابق
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in guarantors.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != guarantors.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('guarantors', page=page_num, search=search) }}">
                                        {{ page_num }}
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if guarantors.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('guarantors', page=guarantors.next_num, search=search) }}">
                                التالي
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-user-shield fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد ضامنين</h4>
                    <p class="text-muted">
                        {% if search %}
                        لم يتم العثور على ضامنين مطابقين لبحثك
                        {% else %}
                        ابدأ بإضافة ضامن جديد
                        {% endif %}
                    </p>
                    {% if not search %}
                    <a href="{{ url_for('add_guarantor') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        إضافة ضامن جديد
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- أزرار إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex gap-2 flex-wrap">
            <button class="btn btn-outline-success" onclick="exportToCSV('guarantorsTable', 'guarantors.csv')">
                <i class="fas fa-file-excel"></i>
                تصدير Excel
            </button>
            <button class="btn btn-outline-info btn-print">
                <i class="fas fa-print"></i>
                طباعة
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تهيئة التلميحات
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
