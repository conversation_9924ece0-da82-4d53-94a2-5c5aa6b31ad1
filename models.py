from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, date
from sqlalchemy import func

db = SQLAlchemy()

class Currency(db.Model):
    """نموذج العملة"""
    __tablename__ = 'currencies'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # اسم العملة
    code = db.Column(db.String(10), nullable=False, unique=True)  # رمز العملة (SAR, USD, EUR)
    symbol = db.Column(db.String(10), nullable=False)  # رمز العملة (ر.س, $, €)
    exchange_rate = db.Column(db.Float, nullable=False, default=1.0)  # سعر الصرف مقابل العملة الأساسية
    is_base = db.Column(db.<PERSON>, default=False)  # هل هي العملة الأساسية
    is_active = db.Column(db.<PERSON>an, default=True)  # هل العملة نشطة
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Currency {self.name} ({self.code})>'

    def format_amount(self, amount):
        """تنسيق المبلغ مع رمز العملة"""
        return f"{amount:,.2f} {self.symbol}"

class Customer(db.Model):
    __tablename__ = 'customers'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    address = db.Column(db.Text)
    national_id = db.Column(db.String(20), unique=True)
    email = db.Column(db.String(100))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    invoices = db.relationship('Invoice', backref='customer', lazy=True)
    receipts = db.relationship('Receipt', backref='customer', lazy=True)
    
    def __repr__(self):
        return f'<Customer {self.name}>'
    
    def get_total_debt(self):
        """حساب إجمالي الديون"""
        total_invoices = sum([invoice.total_amount for invoice in self.invoices])
        total_payments = sum([receipt.amount for receipt in self.receipts])
        return total_invoices - total_payments

class Guarantor(db.Model):
    __tablename__ = 'guarantors'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    address = db.Column(db.Text)
    national_id = db.Column(db.String(20), unique=True)
    relationship = db.Column(db.String(50))  # العلاقة بالعميل
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    invoices = db.relationship('Invoice', backref='guarantor', lazy=True)
    
    def __repr__(self):
        return f'<Guarantor {self.name}>'

class Motorcycle(db.Model):
    __tablename__ = 'motorcycles'
    
    id = db.Column(db.Integer, primary_key=True)
    brand = db.Column(db.String(50), nullable=False)  # الماركة
    model = db.Column(db.String(50), nullable=False)  # الموديل
    year = db.Column(db.Integer)  # سنة الصنع
    engine_size = db.Column(db.String(20))  # حجم المحرك
    color = db.Column(db.String(30))  # اللون
    price = db.Column(db.Float, nullable=False)  # السعر
    currency_id = db.Column(db.Integer, db.ForeignKey('currencies.id'), nullable=False, default=1)  # العملة
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    invoices = db.relationship('Invoice', backref='motorcycle', lazy=True)
    
    def __repr__(self):
        return f'<Motorcycle {self.brand} {self.model}>'

    def get_formatted_price(self):
        """الحصول على السعر مع العملة"""
        if self.currency_ref:
            return self.currency_ref.format_amount(self.price)
        return f"{self.price:,.2f}"

class Invoice(db.Model):
    __tablename__ = 'invoices'
    
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(20), unique=True, nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False)
    guarantor_id = db.Column(db.Integer, db.ForeignKey('guarantors.id'), nullable=False)
    motorcycle_id = db.Column(db.Integer, db.ForeignKey('motorcycles.id'), nullable=False)
    
    # تفاصيل الفاتورة
    total_amount = db.Column(db.Float, nullable=False)  # المبلغ الإجمالي
    down_payment = db.Column(db.Float, default=0)  # الدفعة المقدمة
    remaining_amount = db.Column(db.Float, nullable=False)  # المبلغ المتبقي
    installment_amount = db.Column(db.Float, nullable=False)  # قيمة القسط
    installments_count = db.Column(db.Integer, nullable=False)  # عدد الأقساط
    
    # التواريخ
    invoice_date = db.Column(db.Date, default=date.today)
    first_installment_date = db.Column(db.Date, nullable=False)
    
    # الحالة
    status = db.Column(db.String(20), default='نشط')  # نشط، مكتمل، ملغي
    currency_id = db.Column(db.Integer, db.ForeignKey('currencies.id'), nullable=False, default=1)  # العملة
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    installments = db.relationship('Installment', backref='invoice', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Invoice {self.invoice_number}>'
    
    def get_paid_amount(self):
        """حساب المبلغ المدفوع"""
        return self.down_payment + sum([inst.paid_amount for inst in self.installments if inst.is_paid])
    
    def get_remaining_debt(self):
        """حساب المبلغ المتبقي"""
        return self.total_amount - self.get_paid_amount()

class Installment(db.Model):
    __tablename__ = 'installments'
    
    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'), nullable=False)
    installment_number = db.Column(db.Integer, nullable=False)  # رقم القسط
    amount = db.Column(db.Float, nullable=False)  # قيمة القسط
    due_date = db.Column(db.Date, nullable=False)  # تاريخ الاستحقاق
    paid_amount = db.Column(db.Float, default=0)  # المبلغ المدفوع
    payment_date = db.Column(db.Date)  # تاريخ الدفع
    is_paid = db.Column(db.Boolean, default=False)  # هل تم الدفع
    is_overdue = db.Column(db.Boolean, default=False)  # هل متأخر
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<Installment {self.installment_number} for Invoice {self.invoice_id}>'
    
    def check_overdue(self):
        """فحص إذا كان القسط متأخر"""
        if not self.is_paid and self.due_date < date.today():
            self.is_overdue = True
            return True
        return False

class Receipt(db.Model):
    __tablename__ = 'receipts'
    
    id = db.Column(db.Integer, primary_key=True)
    receipt_number = db.Column(db.String(20), unique=True, nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'))
    installment_id = db.Column(db.Integer, db.ForeignKey('installments.id'))
    
    amount = db.Column(db.Float, nullable=False)  # المبلغ
    payment_method = db.Column(db.String(20), default='نقدي')  # طريقة الدفع
    receipt_date = db.Column(db.Date, default=date.today)
    currency_id = db.Column(db.Integer, db.ForeignKey('currencies.id'), nullable=False, default=1)  # العملة
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<Receipt {self.receipt_number}>'
