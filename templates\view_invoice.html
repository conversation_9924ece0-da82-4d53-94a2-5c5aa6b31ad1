{% extends "base.html" %}

{% block title %}تفاصيل الفاتورة {{ invoice.invoice_number }} - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-file-invoice text-primary"></i>
                تفاصيل الفاتورة {{ invoice.invoice_number }}
            </h1>
            <div class="btn-group">
                <a href="{{ url_for('invoices') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة للفواتير
                </a>
                <a href="{{ url_for('edit_invoice', id=invoice.id) }}" class="btn btn-outline-warning">
                    <i class="fas fa-edit"></i>
                    تعديل الفاتورة
                </a>
                <button class="btn btn-outline-info btn-print" data-print-target="invoice-details">
                    <i class="fas fa-print"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<div id="invoice-details">
    <!-- معلومات الفاتورة الأساسية -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i>
                        معلومات الفاتورة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>رقم الفاتورة:</strong></td>
                                    <td>{{ invoice.invoice_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ الفاتورة:</strong></td>
                                    <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الحالة:</strong></td>
                                    <td>
                                        {% if invoice.status == 'نشط' %}
                                        <span class="badge bg-success">{{ invoice.status }}</span>
                                        {% elif invoice.status == 'مكتمل' %}
                                        <span class="badge bg-primary">{{ invoice.status }}</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ invoice.status }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ أول قسط:</strong></td>
                                    <td>{{ invoice.first_installment_date.strftime('%Y-%m-%d') }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>المبلغ الإجمالي:</strong></td>
                                    <td class="text-success"><strong>{{ "{:,.0f}".format(invoice.total_amount) }} ر.س</strong></td>
                                </tr>
                                <tr>
                                    <td><strong>الدفعة المقدمة:</strong></td>
                                    <td>{{ "{:,.0f}".format(invoice.down_payment) }} ر.س</td>
                                </tr>
                                <tr>
                                    <td><strong>المبلغ المدفوع:</strong></td>
                                    <td class="text-info">{{ "{:,.0f}".format(invoice.get_paid_amount()) }} ر.س</td>
                                </tr>
                                <tr>
                                    <td><strong>المبلغ المتبقي:</strong></td>
                                    <td class="text-warning"><strong>{{ "{:,.0f}".format(invoice.get_remaining_debt()) }} ر.س</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    {% if invoice.notes %}
                    <hr>
                    <div class="row">
                        <div class="col-12">
                            <strong>ملاحظات:</strong>
                            <p class="text-muted">{{ invoice.notes }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-pie"></i>
                        نسبة السداد
                    </h6>
                </div>
                <div class="card-body text-center">
                    {% set paid_percentage = (invoice.get_paid_amount() / invoice.total_amount * 100) if invoice.total_amount > 0 else 0 %}
                    <div class="progress mb-3" style="height: 20px;">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: {{ paid_percentage }}%" 
                             aria-valuenow="{{ paid_percentage }}" 
                             aria-valuemin="0" aria-valuemax="100">
                            {{ "{:.1f}".format(paid_percentage) }}%
                        </div>
                    </div>
                    <p class="mb-0">تم سداد {{ "{:.1f}".format(paid_percentage) }}% من إجمالي المبلغ</p>
                </div>
            </div>
        </div>
    </div>

    <!-- بيانات العميل والضامن والدراجة -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-user"></i>
                        بيانات العميل
                    </h6>
                </div>
                <div class="card-body">
                    <p><strong>الاسم:</strong> {{ invoice.customer.name }}</p>
                    <p><strong>الهاتف:</strong> 
                        <a href="tel:{{ invoice.customer.phone }}">{{ invoice.customer.phone }}</a>
                    </p>
                    {% if invoice.customer.national_id %}
                    <p><strong>رقم الهوية:</strong> {{ invoice.customer.national_id }}</p>
                    {% endif %}
                    {% if invoice.customer.email %}
                    <p><strong>البريد:</strong> 
                        <a href="mailto:{{ invoice.customer.email }}">{{ invoice.customer.email }}</a>
                    </p>
                    {% endif %}
                    {% if invoice.customer.address %}
                    <p><strong>العنوان:</strong> {{ invoice.customer.address }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-user-shield"></i>
                        بيانات الضامن
                    </h6>
                </div>
                <div class="card-body">
                    <p><strong>الاسم:</strong> {{ invoice.guarantor.name }}</p>
                    <p><strong>الهاتف:</strong> 
                        <a href="tel:{{ invoice.guarantor.phone }}">{{ invoice.guarantor.phone }}</a>
                    </p>
                    {% if invoice.guarantor.relationship %}
                    <p><strong>العلاقة:</strong> 
                        <span class="badge bg-info">{{ invoice.guarantor.relationship }}</span>
                    </p>
                    {% endif %}
                    {% if invoice.guarantor.national_id %}
                    <p><strong>رقم الهوية:</strong> {{ invoice.guarantor.national_id }}</p>
                    {% endif %}
                    {% if invoice.guarantor.address %}
                    <p><strong>العنوان:</strong> {{ invoice.guarantor.address }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-motorcycle"></i>
                        بيانات الدراجة النارية
                    </h6>
                </div>
                <div class="card-body">
                    <p><strong>الماركة:</strong> {{ invoice.motorcycle.brand }}</p>
                    <p><strong>الموديل:</strong> {{ invoice.motorcycle.model }}</p>
                    {% if invoice.motorcycle.year %}
                    <p><strong>سنة الصنع:</strong> {{ invoice.motorcycle.year }}</p>
                    {% endif %}
                    {% if invoice.motorcycle.engine_size %}
                    <p><strong>حجم المحرك:</strong> 
                        <span class="badge bg-info">{{ invoice.motorcycle.engine_size }}</span>
                    </p>
                    {% endif %}
                    {% if invoice.motorcycle.color %}
                    <p><strong>اللون:</strong> 
                        <span class="badge bg-secondary">{{ invoice.motorcycle.color }}</span>
                    </p>
                    {% endif %}
                    <p><strong>السعر:</strong> {{ "{:,.0f}".format(invoice.motorcycle.price) }} ر.س</p>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الأقساط -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-alt"></i>
                        جدول الأقساط ({{ invoice.installments_count }} قسط)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم القسط</th>
                                    <th>المبلغ</th>
                                    <th>تاريخ الاستحقاق</th>
                                    <th>المبلغ المدفوع</th>
                                    <th>تاريخ الدفع</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for installment in invoice.installments %}
                                <tr class="{% if installment.is_overdue %}table-danger{% elif installment.is_paid %}table-success{% endif %}">
                                    <td><strong>{{ installment.installment_number }}</strong></td>
                                    <td>{{ "{:,.0f}".format(installment.amount) }} ر.س</td>
                                    <td>{{ installment.due_date.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        {% if installment.paid_amount > 0 %}
                                        {{ "{:,.0f}".format(installment.paid_amount) }} ر.س
                                        {% else %}
                                        -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if installment.payment_date %}
                                        {{ installment.payment_date.strftime('%Y-%m-%d') }}
                                        {% else %}
                                        -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if installment.is_paid %}
                                        <span class="badge bg-success">مدفوع</span>
                                        {% elif installment.is_overdue %}
                                        <span class="badge bg-danger">متأخر</span>
                                        {% else %}
                                        <span class="badge bg-warning">مستحق</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if not installment.is_paid %}
                                        <form method="POST" action="{{ url_for('pay_installment', id=installment.id) }}" style="display: inline;">
                                            <button type="submit" class="btn btn-sm btn-outline-success"
                                                    onclick="return confirm('هل تريد تسجيل هذا القسط كمدفوع؟')">
                                                <i class="fas fa-check"></i>
                                                تسديد
                                            </button>
                                        </form>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحديث حالة الأقساط المتأخرة
document.addEventListener('DOMContentLoaded', function() {
    // فحص الأقساط المتأخرة وتحديث ألوانها
    const today = new Date();
    const installmentRows = document.querySelectorAll('tbody tr');

    installmentRows.forEach(row => {
        const dueDateCell = row.cells[2]; // خلية تاريخ الاستحقاق
        const statusCell = row.cells[5]; // خلية الحالة

        if (dueDateCell && statusCell) {
            const dueDate = new Date(dueDateCell.textContent);
            const statusBadge = statusCell.querySelector('.badge');

            if (statusBadge && statusBadge.textContent.trim() === 'مستحق' && dueDate < today) {
                // تحديث الحالة إلى متأخر
                statusBadge.className = 'badge bg-danger';
                statusBadge.textContent = 'متأخر';
                row.className = 'table-danger';
            }
        }
    });
});

// وظيفة لحساب أيام التأخير
function calculateOverdueDays(dueDate) {
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = today - due;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
}
</script>
{% endblock %}
