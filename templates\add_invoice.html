{% extends "base.html" %}

{% block title %}إنشاء فاتورة جديدة - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block extra_css %}
<style>
/* تحسين مظهر القوائم المنسدلة للبحث */
.dropdown-menu.show {
    display: block;
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    background-color: #fff;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.15s ease-in-out;
    cursor: pointer;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    text-decoration: none;
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item-text {
    padding: 0.5rem 1rem;
    color: #6c757d;
}

/* تحسين مظهر حقول البحث */
.position-relative input[type="text"] {
    border-radius: 0.375rem;
}

.position-relative input[type="text"]:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.required::after {
    content: " *";
    color: red;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-file-invoice text-primary"></i>
                إنشاء فاتورة جديدة
            </h1>
            <a href="{{ url_for('invoices') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right"></i>
                العودة للفواتير
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-invoice"></i>
                    بيانات الفاتورة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- العميل -->
                        <div class="col-md-6 mb-3">
                            <label for="customer_search" class="form-label required">العميل</label>
                            <div class="position-relative">
                                <input type="text" class="form-control" id="customer_search"
                                       placeholder="ابحث عن العميل بالاسم أو الهاتف..."
                                       autocomplete="off" required>
                                <input type="hidden" id="customer_id" name="customer_id" required>
                                <div id="customer_dropdown" class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto;"></div>
                            </div>
                            <div class="invalid-feedback">
                                يرجى اختيار العميل
                            </div>
                        </div>
                        
                        <!-- الضامن -->
                        <div class="col-md-6 mb-3">
                            <label for="guarantor_search" class="form-label required">الضامن</label>
                            <div class="position-relative">
                                <input type="text" class="form-control" id="guarantor_search"
                                       placeholder="ابحث عن الضامن بالاسم أو الهاتف..."
                                       autocomplete="off" required>
                                <input type="hidden" id="guarantor_id" name="guarantor_id" required>
                                <div id="guarantor_dropdown" class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto;"></div>
                            </div>
                            <div class="invalid-feedback">
                                يرجى اختيار الضامن
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- الدراجة النارية -->
                        <div class="col-md-6 mb-3">
                            <label for="motorcycle_search" class="form-label required">الدراجة النارية</label>
                            <div class="position-relative">
                                <input type="text" class="form-control" id="motorcycle_search"
                                       placeholder="ابحث عن الدراجة بالماركة أو الموديل..."
                                       autocomplete="off" required>
                                <input type="hidden" id="motorcycle_id" name="motorcycle_id" required>
                                <div id="motorcycle_dropdown" class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto;"></div>
                            </div>
                            <div class="invalid-feedback">
                                يرجى اختيار الدراجة النارية
                            </div>
                        </div>
                        
                        <!-- المبلغ الإجمالي -->
                        <div class="col-md-4 mb-3">
                            <label for="total_amount" class="form-label required">المبلغ الإجمالي</label>
                            <input type="number" class="form-control" id="total_amount" name="total_amount"
                                   min="1" step="0.01" placeholder="أدخل المبلغ الإجمالي" required>
                            <div class="invalid-feedback">
                                يرجى إدخال المبلغ الإجمالي
                            </div>
                        </div>

                        <!-- العملة -->
                        <div class="col-md-2 mb-3">
                            <label for="currency_id" class="form-label required">العملة</label>
                            <select class="form-select" id="currency_id" name="currency_id" required>
                                {% for currency in currencies %}
                                <option value="{{ currency.id }}"
                                        data-symbol="{{ currency.symbol }}"
                                        {% if currency.is_base %}selected{% endif %}>
                                    {{ currency.symbol }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار العملة
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- الدفعة المقدمة -->
                        <div class="col-md-6 mb-3">
                            <label for="down_payment" class="form-label">الدفعة المقدمة</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="down_payment" name="down_payment"
                                       min="0" step="0.01" value="0">
                                <span class="input-group-text" id="down_payment_currency">ر.س</span>
                            </div>
                        </div>
                        
                        <!-- عدد الأقساط -->
                        <div class="col-md-4 mb-3">
                            <label for="installments_count" class="form-label required">عدد الأقساط</label>
                            <select class="form-select" id="installments_count" name="installments_count" required>
                                <option value="">اختر عدد الأقساط</option>
                                <option value="6">6 أقساط</option>
                                <option value="12" selected>12 قسط</option>
                                <option value="18">18 قسط</option>
                                <option value="24">24 قسط</option>
                                <option value="36">36 قسط</option>
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار عدد الأقساط
                            </div>
                        </div>
                        
                        <!-- تاريخ أول قسط -->
                        <div class="col-md-4 mb-3">
                            <label for="first_installment_date" class="form-label required">تاريخ أول قسط</label>
                            <input type="date" class="form-control" id="first_installment_date" name="first_installment_date" required>
                            <div class="invalid-feedback">
                                يرجى اختيار تاريخ أول قسط
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- المبلغ المتبقي -->
                        <div class="col-md-6 mb-3">
                            <label for="remaining_amount" class="form-label">المبلغ المتبقي</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="remaining_amount" name="remaining_amount"
                                       readonly>
                                <span class="input-group-text" id="remaining_currency">ر.س</span>
                            </div>
                        </div>

                        <!-- قيمة القسط -->
                        <div class="col-md-6 mb-3">
                            <label for="installment_amount" class="form-label">قيمة القسط الواحد</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="installment_amount" name="installment_amount"
                                       readonly>
                                <span class="input-group-text" id="installment_currency">ر.س</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- ملاحظات -->
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="أي ملاحظات إضافية..."></textarea>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="d-flex gap-2 justify-content-end">
                        <a href="{{ url_for('invoices') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            إنشاء الفاتورة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// بيانات العملاء والضامنين والدراجات
let customers = [];
let guarantors = [];
let motorcycles = [];

document.addEventListener('DOMContentLoaded', function() {
    // تحميل البيانات
    loadData();
    // تعيين تاريخ أول قسط إلى الشهر القادم
    const today = new Date();
    const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
    document.getElementById('first_installment_date').value = nextMonth.toISOString().split('T')[0];

    // إزالة القيم عند تغيير الدراجة
    const motorcycleSelect = document.getElementById('motorcycle_id');
    const totalAmountInput = document.getElementById('total_amount');

    motorcycleSelect.addEventListener('change', function() {
        if (!this.value) {
            totalAmountInput.value = '';
            clearCalculations();
        }
    });

    // تحديث الحسابات عند تغيير المبلغ الإجمالي أو الدفعة المقدمة أو عدد الأقساط
    document.getElementById('total_amount').addEventListener('input', calculateInstallments);
    document.getElementById('down_payment').addEventListener('input', calculateInstallments);
    document.getElementById('installments_count').addEventListener('change', calculateInstallments);

    // تحديث رموز العملة عند تغيير العملة
    document.getElementById('currency_id').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const symbol = selectedOption.getAttribute('data-symbol');

        document.getElementById('down_payment_currency').textContent = symbol;
        document.getElementById('remaining_currency').textContent = symbol;
        document.getElementById('installment_currency').textContent = symbol;
    });

    function calculateInstallments() {
        const totalAmount = parseFloat(document.getElementById('total_amount').value) || 0;
        const downPayment = parseFloat(document.getElementById('down_payment').value) || 0;
        const installmentsCount = parseInt(document.getElementById('installments_count').value) || 1;

        if (totalAmount > 0) {
            const remainingAmount = totalAmount - downPayment;
            const installmentAmount = remainingAmount / installmentsCount;

            document.getElementById('remaining_amount').value = remainingAmount.toFixed(2);
            document.getElementById('installment_amount').value = installmentAmount.toFixed(2);
        }
    }

    function clearCalculations() {
        document.getElementById('remaining_amount').value = '';
        document.getElementById('installment_amount').value = '';
    }

    // التحقق من صحة الدفعة المقدمة
    document.getElementById('down_payment').addEventListener('input', function() {
        const totalAmount = parseFloat(document.getElementById('total_amount').value) || 0;
        const downPayment = parseFloat(this.value) || 0;

        if (downPayment > totalAmount) {
            this.value = totalAmount;
            calculateInstallments();
        }
    });

    // حساب القيم الأولية
    calculateInstallments();
});

// تحميل البيانات من الخادم
async function loadData() {
    try {
        // تحميل العملاء
        const customersResponse = await fetch('/api/customers');
        customers = await customersResponse.json();

        // تحميل الضامنين
        const guarantorsResponse = await fetch('/api/guarantors');
        guarantors = await guarantorsResponse.json();

        // تحميل الدراجات
        const motorcyclesResponse = await fetch('/api/motorcycles');
        motorcycles = await motorcyclesResponse.json();

        // تهيئة البحث
        setupSearch();

    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
    }
}

// تهيئة وظائف البحث
function setupSearch() {
    setupCustomerSearch();
    setupGuarantorSearch();
    setupMotorcycleSearch();
}

// إعداد بحث العملاء
function setupCustomerSearch() {
    const searchInput = document.getElementById('customer_search');
    const hiddenInput = document.getElementById('customer_id');
    const dropdown = document.getElementById('customer_dropdown');

    searchInput.addEventListener('input', function() {
        const query = this.value.toLowerCase();
        if (query.length < 1) {
            dropdown.classList.remove('show');
            hiddenInput.value = '';
            return;
        }

        const filtered = customers.filter(customer =>
            customer.name.toLowerCase().includes(query) ||
            customer.phone.includes(query)
        );

        showCustomerDropdown(filtered, dropdown, searchInput, hiddenInput);
    });

    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
            dropdown.classList.remove('show');
        }
    });
}

function showCustomerDropdown(filtered, dropdown, searchInput, hiddenInput) {
    dropdown.innerHTML = '';

    if (filtered.length === 0) {
        dropdown.innerHTML = '<div class="dropdown-item-text text-muted">لا توجد نتائج</div>';
    } else {
        filtered.forEach(customer => {
            const item = document.createElement('a');
            item.className = 'dropdown-item';
            item.href = '#';
            item.innerHTML = `
                <div>
                    <strong>${customer.name}</strong><br>
                    <small class="text-muted">${customer.phone}</small>
                    ${customer.debt > 0 ? `<span class="badge bg-warning ms-2">${customer.debt.toLocaleString()} ر.س</span>` : ''}
                </div>
            `;

            item.addEventListener('click', function(e) {
                e.preventDefault();
                searchInput.value = customer.name;
                hiddenInput.value = customer.id;
                dropdown.classList.remove('show');
            });

            dropdown.appendChild(item);
        });
    }

    dropdown.classList.add('show');
}

// إعداد بحث الضامنين
function setupGuarantorSearch() {
    const searchInput = document.getElementById('guarantor_search');
    const hiddenInput = document.getElementById('guarantor_id');
    const dropdown = document.getElementById('guarantor_dropdown');

    searchInput.addEventListener('input', function() {
        const query = this.value.toLowerCase();
        if (query.length < 1) {
            dropdown.classList.remove('show');
            hiddenInput.value = '';
            return;
        }

        const filtered = guarantors.filter(guarantor =>
            guarantor.name.toLowerCase().includes(query) ||
            guarantor.phone.includes(query)
        );

        showGuarantorDropdown(filtered, dropdown, searchInput, hiddenInput);
    });

    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
            dropdown.classList.remove('show');
        }
    });
}

function showGuarantorDropdown(filtered, dropdown, searchInput, hiddenInput) {
    dropdown.innerHTML = '';

    if (filtered.length === 0) {
        dropdown.innerHTML = '<div class="dropdown-item-text text-muted">لا توجد نتائج</div>';
    } else {
        filtered.forEach(guarantor => {
            const item = document.createElement('a');
            item.className = 'dropdown-item';
            item.href = '#';
            item.innerHTML = `
                <div>
                    <strong>${guarantor.name}</strong><br>
                    <small class="text-muted">${guarantor.phone}</small>
                    ${guarantor.relationship ? `<span class="badge bg-info ms-2">${guarantor.relationship}</span>` : ''}
                </div>
            `;

            item.addEventListener('click', function(e) {
                e.preventDefault();
                searchInput.value = guarantor.name;
                hiddenInput.value = guarantor.id;
                dropdown.classList.remove('show');
            });

            dropdown.appendChild(item);
        });
    }

    dropdown.classList.add('show');
}

// إعداد بحث الدراجات
function setupMotorcycleSearch() {
    const searchInput = document.getElementById('motorcycle_search');
    const hiddenInput = document.getElementById('motorcycle_id');
    const dropdown = document.getElementById('motorcycle_dropdown');

    searchInput.addEventListener('input', function() {
        const query = this.value.toLowerCase();
        if (query.length < 1) {
            dropdown.classList.remove('show');
            hiddenInput.value = '';
            return;
        }

        const filtered = motorcycles.filter(motorcycle =>
            motorcycle.brand.toLowerCase().includes(query) ||
            motorcycle.model.toLowerCase().includes(query) ||
            (motorcycle.color && motorcycle.color.toLowerCase().includes(query))
        );

        showMotorcycleDropdown(filtered, dropdown, searchInput, hiddenInput);
    });

    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
            dropdown.classList.remove('show');
        }
    });
}

function showMotorcycleDropdown(filtered, dropdown, searchInput, hiddenInput) {
    dropdown.innerHTML = '';

    if (filtered.length === 0) {
        dropdown.innerHTML = '<div class="dropdown-item-text text-muted">لا توجد نتائج</div>';
    } else {
        filtered.forEach(motorcycle => {
            const item = document.createElement('a');
            item.className = 'dropdown-item';
            item.href = '#';
            item.innerHTML = `
                <div>
                    <strong>${motorcycle.brand} ${motorcycle.model}</strong><br>
                    <small class="text-muted">
                        ${motorcycle.year ? `${motorcycle.year} - ` : ''}
                        ${motorcycle.color ? `${motorcycle.color} - ` : ''}
                        ${motorcycle.engine_size ? motorcycle.engine_size : ''}
                    </small>
                </div>
            `;

            item.addEventListener('click', function(e) {
                e.preventDefault();
                searchInput.value = `${motorcycle.brand} ${motorcycle.model}`;
                hiddenInput.value = motorcycle.id;
                dropdown.classList.remove('show');
            });

            dropdown.appendChild(item);
        });
    }

    dropdown.classList.add('show');
}
</script>
{% endblock %}
