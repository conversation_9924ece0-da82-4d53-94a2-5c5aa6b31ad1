{% extends "base.html" %}

{% block title %}إنشاء فاتورة جديدة - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-file-invoice text-primary"></i>
                إنشاء فاتورة جديدة
            </h1>
            <a href="{{ url_for('invoices') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right"></i>
                العودة للفواتير
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-invoice"></i>
                    بيانات الفاتورة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- العميل -->
                        <div class="col-md-6 mb-3">
                            <label for="customer_id" class="form-label required">العميل</label>
                            <select class="form-select" id="customer_id" name="customer_id" required>
                                <option value="">اختر العميل</option>
                                {% for customer in customers %}
                                <option value="{{ customer.id }}">{{ customer.name }} - {{ customer.phone }}</option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار العميل
                            </div>
                        </div>
                        
                        <!-- الضامن -->
                        <div class="col-md-6 mb-3">
                            <label for="guarantor_id" class="form-label required">الضامن</label>
                            <select class="form-select" id="guarantor_id" name="guarantor_id" required>
                                <option value="">اختر الضامن</option>
                                {% for guarantor in guarantors %}
                                <option value="{{ guarantor.id }}">{{ guarantor.name }} - {{ guarantor.phone }}</option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار الضامن
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- الدراجة النارية -->
                        <div class="col-md-6 mb-3">
                            <label for="motorcycle_id" class="form-label required">الدراجة النارية</label>
                            <select class="form-select" id="motorcycle_id" name="motorcycle_id" required>
                                <option value="">اختر الدراجة</option>
                                {% for motorcycle in motorcycles %}
                                <option value="{{ motorcycle.id }}" data-price="{{ motorcycle.price }}">
                                    {{ motorcycle.brand }} {{ motorcycle.model }} - {{ "{:,.0f}".format(motorcycle.price) }} ر.س
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار الدراجة النارية
                            </div>
                        </div>
                        
                        <!-- المبلغ الإجمالي -->
                        <div class="col-md-6 mb-3">
                            <label for="total_amount" class="form-label required">المبلغ الإجمالي</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="total_amount" name="total_amount" 
                                       min="1" step="0.01" readonly required>
                                <span class="input-group-text">ر.س</span>
                            </div>
                            <div class="invalid-feedback">
                                يرجى إدخال المبلغ الإجمالي
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- الدفعة المقدمة -->
                        <div class="col-md-4 mb-3">
                            <label for="down_payment" class="form-label">الدفعة المقدمة</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="down_payment" name="down_payment" 
                                       min="0" step="0.01" value="0">
                                <span class="input-group-text">ر.س</span>
                            </div>
                        </div>
                        
                        <!-- عدد الأقساط -->
                        <div class="col-md-4 mb-3">
                            <label for="installments_count" class="form-label required">عدد الأقساط</label>
                            <select class="form-select" id="installments_count" name="installments_count" required>
                                <option value="">اختر عدد الأقساط</option>
                                <option value="6">6 أقساط</option>
                                <option value="12" selected>12 قسط</option>
                                <option value="18">18 قسط</option>
                                <option value="24">24 قسط</option>
                                <option value="36">36 قسط</option>
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار عدد الأقساط
                            </div>
                        </div>
                        
                        <!-- تاريخ أول قسط -->
                        <div class="col-md-4 mb-3">
                            <label for="first_installment_date" class="form-label required">تاريخ أول قسط</label>
                            <input type="date" class="form-control" id="first_installment_date" name="first_installment_date" required>
                            <div class="invalid-feedback">
                                يرجى اختيار تاريخ أول قسط
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- المبلغ المتبقي -->
                        <div class="col-md-6 mb-3">
                            <label for="remaining_amount" class="form-label">المبلغ المتبقي</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="remaining_amount" name="remaining_amount" 
                                       readonly>
                                <span class="input-group-text">ر.س</span>
                            </div>
                        </div>
                        
                        <!-- قيمة القسط -->
                        <div class="col-md-6 mb-3">
                            <label for="installment_amount" class="form-label">قيمة القسط الواحد</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="installment_amount" name="installment_amount" 
                                       readonly>
                                <span class="input-group-text">ر.س</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- ملاحظات -->
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="أي ملاحظات إضافية..."></textarea>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="d-flex gap-2 justify-content-end">
                        <a href="{{ url_for('invoices') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            إنشاء الفاتورة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تعيين تاريخ أول قسط إلى الشهر القادم
    const today = new Date();
    const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
    document.getElementById('first_installment_date').value = nextMonth.toISOString().split('T')[0];

    // تحديث السعر عند اختيار الدراجة
    const motorcycleSelect = document.getElementById('motorcycle_id');
    const totalAmountInput = document.getElementById('total_amount');

    motorcycleSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            const price = parseFloat(selectedOption.getAttribute('data-price'));
            totalAmountInput.value = price;
            calculateInstallments();
        } else {
            totalAmountInput.value = '';
            clearCalculations();
        }
    });

    // تحديث الحسابات عند تغيير الدفعة المقدمة أو عدد الأقساط
    document.getElementById('down_payment').addEventListener('input', calculateInstallments);
    document.getElementById('installments_count').addEventListener('change', calculateInstallments);

    function calculateInstallments() {
        const totalAmount = parseFloat(document.getElementById('total_amount').value) || 0;
        const downPayment = parseFloat(document.getElementById('down_payment').value) || 0;
        const installmentsCount = parseInt(document.getElementById('installments_count').value) || 1;

        if (totalAmount > 0) {
            const remainingAmount = totalAmount - downPayment;
            const installmentAmount = remainingAmount / installmentsCount;

            document.getElementById('remaining_amount').value = remainingAmount.toFixed(2);
            document.getElementById('installment_amount').value = installmentAmount.toFixed(2);
        }
    }

    function clearCalculations() {
        document.getElementById('remaining_amount').value = '';
        document.getElementById('installment_amount').value = '';
    }

    // التحقق من صحة الدفعة المقدمة
    document.getElementById('down_payment').addEventListener('input', function() {
        const totalAmount = parseFloat(document.getElementById('total_amount').value) || 0;
        const downPayment = parseFloat(this.value) || 0;

        if (downPayment > totalAmount) {
            this.value = totalAmount;
            calculateInstallments();
        }
    });
});
</script>
{% endblock %}
