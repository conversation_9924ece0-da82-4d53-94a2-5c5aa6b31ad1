# نظام محاسبة مبيعات الدراجات النارية بنظام الأقساط

نظام شامل لإدارة مبيعات الدراجات النارية بنظام الأقساط، مصمم خصيصاً للشركات العربية مع دعم كامل للغة العربية والاتجاه من اليمين إلى اليسار (RTL).

## الميزات الرئيسية

### 🏠 الصفحة الرئيسية
- لوحة تحكم شاملة مع إحصائيات سريعة
- الوصول السريع لجميع الوظائف
- رسوم بيانية تفاعلية للمبيعات
- عرض الحالة العامة للنظام

### 👥 إدارة العملاء
- إضافة وتعديل وحذف العملاء
- البحث المتقدم في بيانات العملاء
- تتبع إجمالي ديون كل عميل
- عرض تاريخ المعاملات

### 🛡️ إدارة الضامنين
- إدارة بيانات الضامنين
- ربط الضامنين بالعملاء
- تحديد نوع العلاقة (والد، أخ، عم، إلخ)
- كشف حساب شامل للضامنين

### 🏍️ إدارة الدراجات النارية
- كتالوج شامل للدراجات النارية
- تصنيف حسب الماركة والموديل
- إدارة الأسعار والمواصفات
- تتبع المبيعات لكل نوع

### 📄 نظام الفواتير
- إنشاء فواتير مبيعات تلقائية
- حساب الأقساط تلقائياً
- ربط العميل والضامن والدراجة
- تتبع حالة كل فاتورة

### 💰 إدارة الأقساط
- جدولة الأقساط تلقائياً
- تتبع المدفوعات والمتأخرات
- تنبيهات للأقساط المستحقة
- حساب الفوائد والغرامات

### 🧾 سندات القبض
- إصدار سندات قبض للمدفوعات
- ربط السندات بالفواتير والأقساط
- طرق دفع متعددة (نقدي، تحويل، شيك)
- أرشفة وطباعة السندات

### 📊 التقارير والإحصائيات
- تقرير المبيعات الإجمالية
- تقرير العملاء المتأخرين
- تقرير الأقساط المستحقة
- تقرير المبيعات حسب نوع الدراجة
- كشف حساب الضامن
- كشف حساب العميل
- رسوم بيانية تفاعلية

## التقنيات المستخدمة

### الواجهة الخلفية (Backend)
- **Python Flask** - إطار العمل الرئيسي
- **SQLAlchemy** - قاعدة البيانات ORM
- **SQLite** - قاعدة البيانات (قابلة للتطوير لـ MySQL/PostgreSQL)

### الواجهة الأمامية (Frontend)
- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتنسيق
- **Bootstrap 5 RTL** - إطار العمل للتصميم المتجاوب
- **JavaScript** - التفاعل والوظائف الديناميكية
- **Chart.js** - الرسوم البيانية التفاعلية

### الخطوط والتصميم
- **خط Cairo** - خط عربي احترافي من Google Fonts
- **Font Awesome** - أيقونات احترافية
- **تصميم متجاوب** - يعمل على جميع الأجهزة

## متطلبات التشغيل

### متطلبات النظام
- Python 3.8 أو أحدث
- متصفح ويب حديث
- 100 ميجابايت مساحة تخزين

### المكتبات المطلوبة
```
Flask==3.1.1
SQLAlchemy==2.0.41
Flask-SQLAlchemy==3.1.1
python-dateutil==2.9.0
```

## طريقة التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd ashko
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل التطبيق
```bash
python app.py
```

### 4. فتح المتصفح
افتح المتصفح وانتقل إلى: `http://127.0.0.1:5000`

## الاستخدام

### البدء السريع
1. **إضافة دراجة نارية**: انتقل إلى قسم الدراجات وأضف المنتجات المتاحة
2. **إضافة عميل**: سجل بيانات العميل في قسم العملاء
3. **إضافة ضامن**: سجل بيانات الضامن في قسم الضامنين
4. **إنشاء فاتورة**: أنشئ فاتورة مبيعات جديدة مع تحديد نظام الأقساط
5. **تتبع المدفوعات**: استخدم سندات القبض لتسجيل المدفوعات
6. **مراجعة التقارير**: اطلع على التقارير والإحصائيات

### نصائح الاستخدام
- استخدم البحث السريع للعثور على العملاء والفواتير
- راجع التقارير بانتظام لمتابعة الأداء
- اطبع الفواتير وسندات القبض للأرشفة
- احرص على تحديث بيانات الاتصال للعملاء

## الميزات المتقدمة

### التصميم المتجاوب
- يعمل بشكل مثالي على الهواتف الذكية
- تصميم متكيف مع جميع أحجام الشاشات
- واجهة مستخدم سهلة ومريحة

### دعم اللغة العربية
- واجهة كاملة باللغة العربية
- دعم الاتجاه من اليمين إلى اليسار (RTL)
- خطوط عربية احترافية
- تنسيق التواريخ والأرقام بالعربية

### الأمان والموثوقية
- حماية من هجمات SQL Injection
- تشفير البيانات الحساسة
- نسخ احتياطية تلقائية
- تسجيل العمليات (Logging)

## التطوير المستقبلي

### الميزات المخططة
- [ ] تطبيق أندرويد أصلي
- [ ] نظام إشعارات SMS/WhatsApp
- [ ] تكامل مع أنظمة الدفع الإلكتروني
- [ ] تقارير PDF متقدمة
- [ ] نظام صلاحيات متعدد المستخدمين
- [ ] API للتكامل مع أنظمة أخرى

### التحسينات التقنية
- [ ] قاعدة بيانات PostgreSQL
- [ ] نظام Cache للأداء
- [ ] Docker للنشر
- [ ] اختبارات تلقائية
- [ ] CI/CD Pipeline

## الدعم والمساعدة

### الأسئلة الشائعة
**س: كيف يمكنني إضافة ماركة دراجة جديدة؟**
ج: في صفحة إضافة دراجة، اختر "أخرى" من قائمة الماركات وأدخل الاسم الجديد.

**س: كيف يمكنني تعديل نظام الأقساط؟**
ج: يمكن تعديل عدد الأقساط وقيمتها من صفحة إنشاء الفاتورة.

**س: هل يمكن طباعة التقارير؟**
ج: نعم، جميع التقارير قابلة للطباعة والتصدير.

### التواصل
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966 50 123 4567

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## المساهمة

نرحب بمساهماتكم! يرجى قراءة [CONTRIBUTING.md](CONTRIBUTING.md) للتفاصيل.

---

**تم تطوير هذا النظام خصيصاً للشركات العربية مع مراعاة الاحتياجات المحلية والثقافية.**
