{% extends "base.html" %}

{% block title %}التقارير - نظام محاسبة مبيعات الدراجات النارية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-chart-bar text-primary"></i>
            التقارير والإحصائيات
        </h1>
    </div>
</div>

<!-- أزرار التقارير -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-filter"></i>
                    اختر نوع التقرير
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-primary w-100 h-100 report-btn" data-report="sales">
                            <i class="fas fa-chart-line fa-2x mb-2"></i>
                            <br>تقرير المبيعات
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-success w-100 h-100 report-btn" data-report="customers">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <br>تقرير العملاء
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-warning w-100 h-100 report-btn" data-report="overdue">
                            <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                            <br>الأقساط المتأخرة
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-info w-100 h-100 report-btn" data-report="motorcycles">
                            <i class="fas fa-motorcycle fa-2x mb-2"></i>
                            <br>تقرير الدراجات
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-secondary w-100 h-100 report-btn" data-report="guarantors">
                            <i class="fas fa-user-shield fa-2x mb-2"></i>
                            <br>كشف الضامنين
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-dark w-100 h-100 report-btn" data-report="installments">
                            <i class="fas fa-calendar-check fa-2x mb-2"></i>
                            <br>تقرير الأقساط
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- منطقة عرض التقارير -->
<div class="row">
    <div class="col-12">
        <div id="reportContent" class="card" style="display: none;">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0" id="reportTitle">
                    <i class="fas fa-chart-bar"></i>
                    التقرير
                </h5>
                <div class="btn-group">
                    <button class="btn btn-sm btn-outline-success" onclick="exportReport()">
                        <i class="fas fa-file-excel"></i>
                        تصدير
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="printReport()">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                </div>
            </div>
            <div class="card-body" id="reportBody">
                <!-- سيتم ملء المحتوى بـ JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- رسائل التحميل -->
<div class="row">
    <div class="col-12">
        <div id="loadingMessage" class="text-center py-5" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-3">جاري تحضير التقرير...</p>
        </div>
    </div>
</div>

<!-- رسالة ترحيب -->
<div class="row">
    <div class="col-12">
        <div id="welcomeMessage" class="card border-info">
            <div class="card-body text-center">
                <i class="fas fa-chart-pie fa-4x text-info mb-3"></i>
                <h4 class="text-info">مرحباً بك في قسم التقارير</h4>
                <p class="text-muted">اختر نوع التقرير الذي تريد عرضه من الأزرار أعلاه</p>
                <div class="row mt-4">
                    <div class="col-md-3 mb-3">
                        <div class="bg-light p-3 rounded">
                            <i class="fas fa-chart-line text-primary fa-2x"></i>
                            <h6 class="mt-2">تقرير المبيعات</h6>
                            <small class="text-muted">إحصائيات المبيعات والإيرادات</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="bg-light p-3 rounded">
                            <i class="fas fa-users text-success fa-2x"></i>
                            <h6 class="mt-2">تقرير العملاء</h6>
                            <small class="text-muted">قائمة العملاء وحالة حساباتهم</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="bg-light p-3 rounded">
                            <i class="fas fa-exclamation-triangle text-warning fa-2x"></i>
                            <h6 class="mt-2">الأقساط المتأخرة</h6>
                            <small class="text-muted">العملاء المتأخرين في السداد</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="bg-light p-3 rounded">
                            <i class="fas fa-motorcycle text-info fa-2x"></i>
                            <h6 class="mt-2">تقرير الدراجات</h6>
                            <small class="text-muted">إحصائيات المنتجات والمبيعات</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const reportButtons = document.querySelectorAll('.report-btn');
    const reportContent = document.getElementById('reportContent');
    const reportTitle = document.getElementById('reportTitle');
    const reportBody = document.getElementById('reportBody');
    const loadingMessage = document.getElementById('loadingMessage');
    const welcomeMessage = document.getElementById('welcomeMessage');
    
    reportButtons.forEach(button => {
        button.addEventListener('click', function() {
            const reportType = this.getAttribute('data-report');
            loadReport(reportType);
        });
    });
    
    function loadReport(reportType) {
        // إخفاء الرسائل وإظهار التحميل
        welcomeMessage.style.display = 'none';
        reportContent.style.display = 'none';
        loadingMessage.style.display = 'block';
        
        // محاكاة تحميل البيانات
        setTimeout(() => {
            loadingMessage.style.display = 'none';
            reportContent.style.display = 'block';
            
            switch(reportType) {
                case 'sales':
                    showSalesReport();
                    break;
                case 'customers':
                    showCustomersReport();
                    break;
                case 'overdue':
                    showOverdueReport();
                    break;
                case 'motorcycles':
                    showMotorcyclesReport();
                    break;
                case 'guarantors':
                    showGuarantorsReport();
                    break;
                case 'installments':
                    showInstallmentsReport();
                    break;
            }
        }, 1000);
    }
    
    function showSalesReport() {
        reportTitle.innerHTML = '<i class="fas fa-chart-line"></i> تقرير المبيعات الإجمالية';
        reportBody.innerHTML = `
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4>150,000 ر.س</h4>
                            <p class="mb-0">إجمالي المبيعات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4>85,000 ر.س</h4>
                            <p class="mb-0">المبلغ المحصل</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h4>65,000 ر.س</h4>
                            <p class="mb-0">المبلغ المتبقي</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h4>15</h4>
                            <p class="mb-0">عدد الفواتير</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <canvas id="salesChart" height="100"></canvas>
                </div>
            </div>
        `;
        
        // رسم بياني للمبيعات
        setTimeout(() => {
            const ctx = document.getElementById('salesChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'المبيعات (ر.س)',
                        data: [25000, 30000, 20000, 35000, 28000, 32000],
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }, 100);
    }
    
    function showCustomersReport() {
        reportTitle.innerHTML = '<i class="fas fa-users"></i> تقرير العملاء';
        reportBody.innerHTML = `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>اسم العميل</th>
                            <th>رقم الهاتف</th>
                            <th>إجمالي المشتريات</th>
                            <th>المبلغ المدفوع</th>
                            <th>المبلغ المتبقي</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>أحمد محمد علي</td>
                            <td>0501234567</td>
                            <td>15,000 ر.س</td>
                            <td>8,000 ر.س</td>
                            <td>7,000 ر.س</td>
                            <td><span class="badge bg-warning">متأخر</span></td>
                        </tr>
                        <tr>
                            <td>محمد عبدالله السعد</td>
                            <td>0509876543</td>
                            <td>20,000 ر.س</td>
                            <td>20,000 ر.س</td>
                            <td>0 ر.س</td>
                            <td><span class="badge bg-success">مكتمل</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
    }
    
    function showOverdueReport() {
        reportTitle.innerHTML = '<i class="fas fa-exclamation-triangle"></i> تقرير الأقساط المتأخرة';
        reportBody.innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                يوجد <strong>5 عملاء</strong> متأخرين في السداد بإجمالي <strong>25,000 ر.س</strong>
            </div>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>اسم العميل</th>
                            <th>رقم الفاتورة</th>
                            <th>رقم القسط</th>
                            <th>المبلغ المستحق</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>أيام التأخير</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="table-danger">
                            <td>أحمد محمد علي</td>
                            <td>INV-20240001</td>
                            <td>3</td>
                            <td>1,250 ر.س</td>
                            <td>2024-11-15</td>
                            <td>15 يوم</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
    }
    
    function showMotorcyclesReport() {
        reportTitle.innerHTML = '<i class="fas fa-motorcycle"></i> تقرير مبيعات الدراجات النارية';
        reportBody.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <canvas id="motorcycleChart" height="200"></canvas>
                </div>
                <div class="col-md-6">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الماركة</th>
                                    <th>عدد المبيعات</th>
                                    <th>الإيرادات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>هوندا</td>
                                    <td>8</td>
                                    <td>120,000 ر.س</td>
                                </tr>
                                <tr>
                                    <td>ياماها</td>
                                    <td>5</td>
                                    <td>80,000 ر.س</td>
                                </tr>
                                <tr>
                                    <td>سوزوكي</td>
                                    <td>3</td>
                                    <td>42,000 ر.س</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
        
        // رسم بياني دائري للدراجات
        setTimeout(() => {
            const ctx = document.getElementById('motorcycleChart').getContext('2d');
            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['هوندا', 'ياماها', 'سوزوكي'],
                    datasets: [{
                        data: [8, 5, 3],
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.2)',
                            'rgba(54, 162, 235, 0.2)',
                            'rgba(255, 205, 86, 0.2)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 205, 86, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true
                }
            });
        }, 100);
    }
    
    function showGuarantorsReport() {
        reportTitle.innerHTML = '<i class="fas fa-user-shield"></i> كشف حساب الضامنين';
        reportBody.innerHTML = `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>اسم الضامن</th>
                            <th>عدد العملاء المضمونين</th>
                            <th>إجمالي المبالغ المضمونة</th>
                            <th>المبالغ المتبقية</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>عبدالله أحمد علي</td>
                            <td>2</td>
                            <td>35,000 ر.س</td>
                            <td>15,000 ر.س</td>
                        </tr>
                        <tr>
                            <td>سعد عبدالله المحمد</td>
                            <td>1</td>
                            <td>20,000 ر.س</td>
                            <td>0 ر.س</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
    }
    
    function showInstallmentsReport() {
        reportTitle.innerHTML = '<i class="fas fa-calendar-check"></i> تقرير الأقساط المستحقة';
        reportBody.innerHTML = `
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h5>45</h5>
                            <p class="mb-0">أقساط مدفوعة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h5>12</h5>
                            <p class="mb-0">أقساط مستحقة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h5>5</h5>
                            <p class="mb-0">أقساط متأخرة</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>العميل</th>
                            <th>رقم الفاتورة</th>
                            <th>رقم القسط</th>
                            <th>المبلغ</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>أحمد محمد علي</td>
                            <td>INV-20240001</td>
                            <td>4</td>
                            <td>1,250 ر.س</td>
                            <td>2024-12-15</td>
                            <td><span class="badge bg-warning">مستحق</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
    }
});

function exportReport() {
    showNotification('جاري تصدير التقرير...', 'info');
    // هنا يمكن إضافة كود التصدير الفعلي
}

function printReport() {
    window.print();
}
</script>
{% endblock %}
