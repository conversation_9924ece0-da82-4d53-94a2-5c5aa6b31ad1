from models import db, Customer, Guarantor, Motorcycle, Invoice, Installment, Receipt
from datetime import datetime, date, timedelta
import random

def init_database(app):
    """تهيئة قاعدة البيانات"""
    with app.app_context():
        db.create_all()
        
        # إضافة بيانات تجريبية إذا كانت قاعدة البيانات فارغة
        if Customer.query.count() == 0:
            create_sample_data()

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    
    # إضافة دراجات نارية
    motorcycles = [
        Motorcycle(brand="هوندا", model="CBR 150", year=2024, engine_size="150cc", color="أحمر", price=15000),
        Motorcycle(brand="ياماها", model="YZF-R15", year=2024, engine_size="155cc", color="أزرق", price=16000),
        Motorcycle(brand="سوزوكي", model="GSX-R125", year=2023, engine_size="125cc", color="أسود", price=14000),
        Motorcycle(brand="كاواساكي", model="Ninja 250", year=2024, engine_size="250cc", color="أخضر", price=20000),
        Motorcycle(brand="هوندا", model="CB 125", year=2023, engine_size="125cc", color="أبيض", price=12000),
    ]
    
    for motorcycle in motorcycles:
        db.session.add(motorcycle)
    
    # إضافة عملاء
    customers = [
        Customer(name="أحمد محمد علي", phone="0501234567", address="الرياض - حي النخيل", national_id="1234567890", email="<EMAIL>"),
        Customer(name="محمد عبدالله السعد", phone="0509876543", address="جدة - حي الصفا", national_id="2345678901", email="<EMAIL>"),
        Customer(name="عبدالرحمن خالد", phone="0551234567", address="الدمام - حي الشاطئ", national_id="3456789012", email="<EMAIL>"),
        Customer(name="فهد سعد المطيري", phone="0561234567", address="الرياض - حي العليا", national_id="4567890123", email="<EMAIL>"),
        Customer(name="سالم أحمد القحطاني", phone="0571234567", address="مكة - حي العزيزية", national_id="5678901234", email="<EMAIL>"),
    ]
    
    for customer in customers:
        db.session.add(customer)
    
    # إضافة ضامنين
    guarantors = [
        Guarantor(name="عبدالله أحمد علي", phone="0501111111", address="الرياض - حي النخيل", national_id="1111111111", relationship="والد"),
        Guarantor(name="سعد عبدالله المحمد", phone="0502222222", address="جدة - حي الصفا", national_id="2222222222", relationship="أخ"),
        Guarantor(name="خالد عبدالرحمن", phone="0503333333", address="الدمام - حي الشاطئ", national_id="3333333333", relationship="عم"),
        Guarantor(name="محمد فهد المطيري", phone="0504444444", address="الرياض - حي العليا", national_id="4444444444", relationship="والد"),
        Guarantor(name="أحمد سالم القحطاني", phone="0505555555", address="مكة - حي العزيزية", national_id="5555555555", relationship="أخ"),
    ]
    
    for guarantor in guarantors:
        db.session.add(guarantor)
    
    db.session.commit()
    
    # إضافة فواتير وأقساط
    for i in range(5):
        customer = customers[i]
        guarantor = guarantors[i]
        motorcycle = motorcycles[i]
        
        # إنشاء فاتورة
        invoice = Invoice(
            invoice_number=f"INV-{2024}{str(i+1).zfill(4)}",
            customer_id=customer.id,
            guarantor_id=guarantor.id,
            motorcycle_id=motorcycle.id,
            total_amount=motorcycle.price,
            down_payment=motorcycle.price * 0.2,  # 20% دفعة مقدمة
            remaining_amount=motorcycle.price * 0.8,
            installment_amount=(motorcycle.price * 0.8) / 12,  # 12 قسط
            installments_count=12,
            invoice_date=date.today() - timedelta(days=random.randint(30, 180)),
            first_installment_date=date.today() - timedelta(days=random.randint(0, 150)),
            status="نشط"
        )
        
        db.session.add(invoice)
        db.session.commit()
        
        # إنشاء الأقساط
        for j in range(12):
            installment_date = invoice.first_installment_date + timedelta(days=30*j)
            is_paid = j < random.randint(2, 8)  # بعض الأقساط مدفوعة
            
            installment = Installment(
                invoice_id=invoice.id,
                installment_number=j+1,
                amount=invoice.installment_amount,
                due_date=installment_date,
                paid_amount=invoice.installment_amount if is_paid else 0,
                payment_date=installment_date if is_paid else None,
                is_paid=is_paid,
                is_overdue=not is_paid and installment_date < date.today()
            )
            
            db.session.add(installment)
            
            # إنشاء سند قبض للأقساط المدفوعة
            if is_paid:
                receipt = Receipt(
                    receipt_number=f"REC-{2024}{str(invoice.id).zfill(2)}{str(j+1).zfill(2)}",
                    customer_id=customer.id,
                    invoice_id=invoice.id,
                    installment_id=installment.id,
                    amount=invoice.installment_amount,
                    payment_method="نقدي",
                    receipt_date=installment_date
                )
                db.session.add(receipt)
    
    db.session.commit()
    print("تم إنشاء البيانات التجريبية بنجاح!")

def get_dashboard_stats():
    """إحصائيات لوحة التحكم"""
    stats = {
        'total_customers': Customer.query.count(),
        'total_invoices': Invoice.query.count(),
        'total_sales': db.session.query(db.func.sum(Invoice.total_amount)).scalar() or 0,
        'overdue_installments': Installment.query.filter_by(is_overdue=True).count(),
        'active_invoices': Invoice.query.filter_by(status='نشط').count(),
    }
    return stats
