// وظائف JavaScript الرئيسية

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة التطبيق
    initializeApp();
});

function initializeApp() {
    // إخفاء التنبيهات تلقائياً بعد 5 ثوان
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
    
    // تهيئة التلميحات
    initializeTooltips();
    
    // تهيئة التحقق من النماذج
    initializeFormValidation();
    
    // تهيئة البحث المباشر
    initializeLiveSearch();
    
    // تهيئة الطباعة
    initializePrint();
}

function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function initializeFormValidation() {
    // التحقق من النماذج
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
    
    // التحقق من الحقول المطلوبة
    const requiredFields = document.querySelectorAll('input[required], select[required], textarea[required]');
    requiredFields.forEach(function(field) {
        field.addEventListener('blur', function() {
            validateField(field);
        });
    });
}

function validateField(field) {
    const value = field.value.trim();
    const fieldName = field.getAttribute('name');
    
    // إزالة رسائل الخطأ السابقة
    const existingError = field.parentNode.querySelector('.invalid-feedback');
    if (existingError) {
        existingError.remove();
    }
    
    field.classList.remove('is-invalid', 'is-valid');
    
    if (!value) {
        showFieldError(field, 'هذا الحقل مطلوب');
        return false;
    }
    
    // التحقق من أنواع الحقول المختلفة
    switch (fieldName) {
        case 'phone':
            if (!isValidPhone(value)) {
                showFieldError(field, 'رقم الهاتف غير صحيح');
                return false;
            }
            break;
        case 'email':
            if (value && !isValidEmail(value)) {
                showFieldError(field, 'البريد الإلكتروني غير صحيح');
                return false;
            }
            break;
        case 'national_id':
            if (value && !isValidNationalId(value)) {
                showFieldError(field, 'رقم الهوية غير صحيح');
                return false;
            }
            break;
        case 'price':
        case 'total_amount':
        case 'down_payment':
        case 'installment_amount':
            if (isNaN(value) || parseFloat(value) <= 0) {
                showFieldError(field, 'يجب أن يكون المبلغ رقماً موجباً');
                return false;
            }
            break;
    }
    
    field.classList.add('is-valid');
    return true;
}

function showFieldError(field, message) {
    field.classList.add('is-invalid');
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

function isValidPhone(phone) {
    const phoneRegex = /^(05|5)[0-9]{8}$/;
    return phoneRegex.test(phone.replace(/\s+/g, ''));
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidNationalId(nationalId) {
    return nationalId.length === 10 && /^\d+$/.test(nationalId);
}

function initializeLiveSearch() {
    const searchInputs = document.querySelectorAll('input[name="search"]');
    
    searchInputs.forEach(function(input) {
        let timeout;
        
        input.addEventListener('input', function() {
            clearTimeout(timeout);
            
            timeout = setTimeout(function() {
                const form = input.closest('form');
                if (form) {
                    form.submit();
                }
            }, 500);
        });
    });
}

function initializePrint() {
    const printButtons = document.querySelectorAll('.btn-print');
    
    printButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const printContent = button.getAttribute('data-print-target');
            if (printContent) {
                printElement(printContent);
            } else {
                window.print();
            }
        });
    });
}

function printElement(elementId) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>طباعة</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <style>
                body { font-family: 'Cairo', sans-serif; }
                @media print {
                    .no-print { display: none !important; }
                }
            </style>
        </head>
        <body>
            ${element.innerHTML}
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    
    setTimeout(function() {
        printWindow.print();
        printWindow.close();
    }, 250);
}

// وظائف مساعدة للحسابات
function calculateInstallments() {
    const totalAmount = parseFloat(document.getElementById('total_amount')?.value || 0);
    const downPayment = parseFloat(document.getElementById('down_payment')?.value || 0);
    const installmentsCount = parseInt(document.getElementById('installments_count')?.value || 1);
    
    const remainingAmount = totalAmount - downPayment;
    const installmentAmount = remainingAmount / installmentsCount;
    
    const remainingAmountField = document.getElementById('remaining_amount');
    const installmentAmountField = document.getElementById('installment_amount');
    
    if (remainingAmountField) {
        remainingAmountField.value = remainingAmount.toFixed(2);
    }
    
    if (installmentAmountField) {
        installmentAmountField.value = installmentAmount.toFixed(2);
    }
}

// تحديث الحسابات عند تغيير القيم
document.addEventListener('input', function(e) {
    if (['total_amount', 'down_payment', 'installments_count'].includes(e.target.id)) {
        calculateInstallments();
    }
});

// وظائف التأكيد
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return confirm(message);
}

// وظائف التحميل
function showLoading(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<span class="loading"></span> جاري التحميل...';
    button.disabled = true;
    
    return function() {
        button.innerHTML = originalText;
        button.disabled = false;
    };
}

// وظائف التصدير
function exportToCSV(tableId, filename = 'data.csv') {
    const table = document.getElementById(tableId);
    if (!table) return;
    
    let csv = [];
    const rows = table.querySelectorAll('tr');
    
    for (let i = 0; i < rows.length; i++) {
        const row = [];
        const cols = rows[i].querySelectorAll('td, th');
        
        for (let j = 0; j < cols.length; j++) {
            row.push(cols[j].innerText);
        }
        
        csv.push(row.join(','));
    }
    
    downloadCSV(csv.join('\n'), filename);
}

function downloadCSV(csv, filename) {
    const csvFile = new Blob([csv], { type: 'text/csv' });
    const downloadLink = document.createElement('a');
    
    downloadLink.download = filename;
    downloadLink.href = window.URL.createObjectURL(csvFile);
    downloadLink.style.display = 'none';
    
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
}

// وظائف التنسيق
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(amount);
}

function formatDate(date) {
    return new Intl.DateTimeFormat('ar-SA').format(new Date(date));
}

// وظائف الإشعارات
function showNotification(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(function() {
        const bsAlert = new bootstrap.Alert(alertDiv);
        bsAlert.close();
    }, 5000);
}
