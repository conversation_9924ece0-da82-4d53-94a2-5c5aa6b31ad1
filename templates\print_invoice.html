<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة الفاتورة - {{ invoice.invoice_number }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            body { font-size: 12px; }
            .page-break { page-break-after: always; }
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #fff;
        }
        
        .invoice-header {
            border-bottom: 3px solid #007bff;
            margin-bottom: 30px;
            padding-bottom: 20px;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .invoice-title {
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .invoice-number {
            font-size: 18px;
            color: #6c757d;
        }
        
        .info-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .amount-highlight {
            background-color: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
        }
        
        .table th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        
        .signature-section {
            margin-top: 50px;
            border-top: 1px solid #dee2e6;
            padding-top: 30px;
        }
        
        .signature-box {
            border: 1px solid #dee2e6;
            height: 80px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            <div class="company-info">
                <h1 class="invoice-title">نظام محاسبة مبيعات الدراجات النارية</h1>
                <p class="mb-1">العنوان: المملكة العربية السعودية</p>
                <p class="mb-1">الهاتف: +966 50 123 4567</p>
                <p class="mb-0">البريد الإلكتروني: <EMAIL></p>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <h2 class="invoice-number">فاتورة رقم: {{ invoice.invoice_number }}</h2>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-1"><strong>تاريخ الفاتورة:</strong> {{ invoice.invoice_date.strftime('%Y-%m-%d') }}</p>
                    <p class="mb-0"><strong>تاريخ أول قسط:</strong> {{ invoice.first_installment_date.strftime('%Y-%m-%d') }}</p>
                </div>
            </div>
        </div>

        <!-- معلومات العميل والضامن -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="info-section">
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-user"></i>
                        معلومات العميل
                    </h5>
                    <table class="table table-sm table-borderless">
                        <tr>
                            <td><strong>الاسم:</strong></td>
                            <td>{{ invoice.customer.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>الهاتف:</strong></td>
                            <td>{{ invoice.customer.phone }}</td>
                        </tr>
                        {% if invoice.customer.national_id %}
                        <tr>
                            <td><strong>الهوية:</strong></td>
                            <td>{{ invoice.customer.national_id }}</td>
                        </tr>
                        {% endif %}
                        {% if invoice.customer.address %}
                        <tr>
                            <td><strong>العنوان:</strong></td>
                            <td>{{ invoice.customer.address }}</td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="info-section">
                    <h5 class="text-success mb-3">
                        <i class="fas fa-user-shield"></i>
                        معلومات الضامن
                    </h5>
                    <table class="table table-sm table-borderless">
                        <tr>
                            <td><strong>الاسم:</strong></td>
                            <td>{{ invoice.guarantor.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>الهاتف:</strong></td>
                            <td>{{ invoice.guarantor.phone }}</td>
                        </tr>
                        {% if invoice.guarantor.national_id %}
                        <tr>
                            <td><strong>الهوية:</strong></td>
                            <td>{{ invoice.guarantor.national_id }}</td>
                        </tr>
                        {% endif %}
                        {% if invoice.guarantor.relationship %}
                        <tr>
                            <td><strong>العلاقة:</strong></td>
                            <td>{{ invoice.guarantor.relationship }}</td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
            </div>
        </div>

        <!-- معلومات الدراجة -->
        <div class="info-section mb-4">
            <h5 class="text-warning mb-3">
                <i class="fas fa-motorcycle"></i>
                معلومات الدراجة النارية
            </h5>
            <div class="row">
                <div class="col-md-3">
                    <strong>الماركة:</strong> {{ invoice.motorcycle.brand }}
                </div>
                <div class="col-md-3">
                    <strong>الموديل:</strong> {{ invoice.motorcycle.model }}
                </div>
                {% if invoice.motorcycle.year %}
                <div class="col-md-3">
                    <strong>السنة:</strong> {{ invoice.motorcycle.year }}
                </div>
                {% endif %}
                {% if invoice.motorcycle.color %}
                <div class="col-md-3">
                    <strong>اللون:</strong> {{ invoice.motorcycle.color }}
                </div>
                {% endif %}
            </div>
        </div>

        <!-- المبالغ المالية -->
        <div class="row mb-4">
            <div class="col-md-8">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>البيان</th>
                            <th class="text-end">المبلغ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>المبلغ الإجمالي</strong></td>
                            <td class="text-end">
                                <strong>{{ "{:,.0f}".format(invoice.total_amount) }} 
                                {{ invoice.currency_ref.symbol if invoice.currency_ref else 'ر.س' }}</strong>
                            </td>
                        </tr>
                        <tr>
                            <td>الدفعة المقدمة</td>
                            <td class="text-end">
                                {{ "{:,.0f}".format(invoice.down_payment) }} 
                                {{ invoice.currency_ref.symbol if invoice.currency_ref else 'ر.س' }}
                            </td>
                        </tr>
                        <tr class="table-warning">
                            <td><strong>المبلغ المتبقي</strong></td>
                            <td class="text-end">
                                <strong>{{ "{:,.0f}".format(invoice.remaining_amount) }} 
                                {{ invoice.currency_ref.symbol if invoice.currency_ref else 'ر.س' }}</strong>
                            </td>
                        </tr>
                        <tr>
                            <td>عدد الأقساط</td>
                            <td class="text-end">{{ invoice.installments_count }} قسط</td>
                        </tr>
                        <tr class="table-info">
                            <td><strong>قيمة القسط الواحد</strong></td>
                            <td class="text-end">
                                <strong>{{ "{:,.0f}".format(invoice.installment_amount) }} 
                                {{ invoice.currency_ref.symbol if invoice.currency_ref else 'ر.س' }}</strong>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="col-md-4">
                <div class="amount-highlight text-center">
                    <h6 class="mb-2">إجمالي المبلغ المستحق</h6>
                    <h3 class="text-primary mb-0">
                        {{ "{:,.0f}".format(invoice.total_amount) }}
                        <small>{{ invoice.currency_ref.symbol if invoice.currency_ref else 'ر.س' }}</small>
                    </h3>
                </div>
            </div>
        </div>

        <!-- الملاحظات -->
        {% if invoice.notes %}
        <div class="info-section mb-4">
            <h5 class="text-secondary mb-3">
                <i class="fas fa-sticky-note"></i>
                ملاحظات
            </h5>
            <p class="mb-0">{{ invoice.notes }}</p>
        </div>
        {% endif %}

        <!-- التوقيعات -->
        <div class="signature-section">
            <div class="row">
                <div class="col-md-4 text-center">
                    <p><strong>توقيع العميل</strong></p>
                    <div class="signature-box"></div>
                    <p class="mt-2 small">{{ invoice.customer.name }}</p>
                </div>
                <div class="col-md-4 text-center">
                    <p><strong>توقيع الضامن</strong></p>
                    <div class="signature-box"></div>
                    <p class="mt-2 small">{{ invoice.guarantor.name }}</p>
                </div>
                <div class="col-md-4 text-center">
                    <p><strong>توقيع المندوب</strong></p>
                    <div class="signature-box"></div>
                    <p class="mt-2 small">إدارة المبيعات</p>
                </div>
            </div>
        </div>

        <!-- أزرار الطباعة -->
        <div class="text-center mt-4 no-print">
            <button onclick="window.print()" class="btn btn-primary btn-lg">
                <i class="fas fa-print"></i>
                طباعة الفاتورة
            </button>
            <button onclick="window.close()" class="btn btn-secondary btn-lg ms-2">
                <i class="fas fa-times"></i>
                إغلاق
            </button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // طباعة تلقائية عند تحميل الصفحة (اختياري)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
