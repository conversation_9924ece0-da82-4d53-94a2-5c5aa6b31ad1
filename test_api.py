#!/usr/bin/env python3
"""
اختبار APIs للتأكد من عملها
"""

from app import app
from models import db, Invoice
import json

def test_api():
    """اختبار API الفواتير"""
    with app.app_context():
        try:
            # الحصول على أول فاتورة
            invoice = Invoice.query.first()
            if not invoice:
                print("لا توجد فواتير في قاعدة البيانات")
                return
            
            print(f"اختبار الفاتورة رقم: {invoice.id}")
            
            # اختبار API الفاتورة
            with app.test_client() as client:
                response = client.get(f'/api/invoice/{invoice.id}')
                print(f"حالة الاستجابة: {response.status_code}")
                print(f"نوع المحتوى: {response.content_type}")
                
                if response.status_code == 200:
                    try:
                        data = response.get_json()
                        print("✅ API الفاتورة يعمل بشكل صحيح")
                        print(f"رقم الفاتورة: {data.get('invoice_number', 'غير موجود')}")
                    except Exception as e:
                        print(f"❌ خطأ في تحليل JSON: {e}")
                        print(f"المحتوى: {response.get_data(as_text=True)[:200]}...")
                else:
                    print(f"❌ خطأ في API: {response.status_code}")
                    print(f"المحتوى: {response.get_data(as_text=True)[:200]}...")
                
                # اختبار API الأقساط
                response2 = client.get(f'/api/invoice/{invoice.id}/installments')
                print(f"\nحالة استجابة الأقساط: {response2.status_code}")
                
                if response2.status_code == 200:
                    try:
                        data2 = response2.get_json()
                        print("✅ API الأقساط يعمل بشكل صحيح")
                        print(f"عدد الأقساط: {len(data2.get('installments', []))}")
                    except Exception as e:
                        print(f"❌ خطأ في تحليل JSON للأقساط: {e}")
                        print(f"المحتوى: {response2.get_data(as_text=True)[:200]}...")
                else:
                    print(f"❌ خطأ في API الأقساط: {response2.status_code}")
                    print(f"المحتوى: {response2.get_data(as_text=True)[:200]}...")
                    
        except Exception as e:
            print(f"خطأ عام في الاختبار: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    print("=" * 50)
    print("اختبار APIs")
    print("=" * 50)
    test_api()
    print("=" * 50)
