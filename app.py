from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from models import db, Customer, Guarantor, Motorcycle, Invoice, Installment, Receipt, Currency
from database import init_database, get_dashboard_stats
from config import Config
from datetime import datetime, date, timedelta
from sqlalchemy import func, or_
import json

app = Flask(__name__)
app.config.from_object(Config)

# تهيئة قاعدة البيانات
db.init_app(app)

# تهيئة قاعدة البيانات عند بدء التطبيق
with app.app_context():
    init_database(app)

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    stats = get_dashboard_stats()
    return render_template('index.html', stats=stats)

@app.route('/customers')
def customers():
    """صفحة العملاء"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    
    query = Customer.query
    if search:
        query = query.filter(or_(
            Customer.name.contains(search),
            Customer.phone.contains(search),
            Customer.national_id.contains(search)
        ))
    
    customers = query.paginate(
        page=page, per_page=10, error_out=False
    )
    
    return render_template('customers.html', customers=customers, search=search)

@app.route('/customers/add', methods=['GET', 'POST'])
def add_customer():
    """إضافة عميل جديد"""
    if request.method == 'POST':
        try:
            customer = Customer(
                name=request.form['name'],
                phone=request.form['phone'],
                address=request.form.get('address', ''),
                national_id=request.form.get('national_id', ''),
                email=request.form.get('email', '')
            )
            db.session.add(customer)
            db.session.commit()
            flash('تم إضافة العميل بنجاح', 'success')
            return redirect(url_for('customers'))
        except Exception as e:
            flash(f'خطأ في إضافة العميل: {str(e)}', 'error')
    
    return render_template('add_customer.html')

@app.route('/customers/edit/<int:id>', methods=['GET', 'POST'])
def edit_customer(id):
    """تعديل بيانات العميل"""
    customer = Customer.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            customer.name = request.form['name']
            customer.phone = request.form['phone']
            customer.address = request.form.get('address', '')
            customer.national_id = request.form.get('national_id', '')
            customer.email = request.form.get('email', '')
            
            db.session.commit()
            flash('تم تحديث بيانات العميل بنجاح', 'success')
            return redirect(url_for('customers'))
        except Exception as e:
            flash(f'خطأ في تحديث البيانات: {str(e)}', 'error')
    
    return render_template('edit_customer.html', customer=customer)

@app.route('/customers/delete/<int:id>')
def delete_customer(id):
    """حذف العميل"""
    try:
        customer = Customer.query.get_or_404(id)
        db.session.delete(customer)
        db.session.commit()
        flash('تم حذف العميل بنجاح', 'success')
    except Exception as e:
        flash(f'خطأ في حذف العميل: {str(e)}', 'error')
    
    return redirect(url_for('customers'))

@app.route('/guarantors')
def guarantors():
    """صفحة الضامنين"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    
    query = Guarantor.query
    if search:
        query = query.filter(or_(
            Guarantor.name.contains(search),
            Guarantor.phone.contains(search),
            Guarantor.national_id.contains(search)
        ))
    
    guarantors = query.paginate(
        page=page, per_page=10, error_out=False
    )
    
    return render_template('guarantors.html', guarantors=guarantors, search=search)

@app.route('/guarantors/add', methods=['GET', 'POST'])
def add_guarantor():
    """إضافة ضامن جديد"""
    if request.method == 'POST':
        try:
            guarantor = Guarantor(
                name=request.form['name'],
                phone=request.form['phone'],
                address=request.form.get('address', ''),
                national_id=request.form.get('national_id', ''),
                relationship=request.form.get('relationship', '')
            )
            db.session.add(guarantor)
            db.session.commit()
            flash('تم إضافة الضامن بنجاح', 'success')
            return redirect(url_for('guarantors'))
        except Exception as e:
            flash(f'خطأ في إضافة الضامن: {str(e)}', 'error')

    return render_template('add_guarantor.html')

@app.route('/guarantors/edit/<int:id>', methods=['GET', 'POST'])
def edit_guarantor(id):
    """تعديل بيانات الضامن"""
    guarantor = Guarantor.query.get_or_404(id)

    if request.method == 'POST':
        try:
            guarantor.name = request.form['name']
            guarantor.phone = request.form['phone']
            guarantor.address = request.form.get('address', '')
            guarantor.national_id = request.form.get('national_id', '')
            guarantor.relationship = request.form.get('relationship', '')

            db.session.commit()
            flash('تم تحديث بيانات الضامن بنجاح', 'success')
            return redirect(url_for('guarantors'))
        except Exception as e:
            flash(f'خطأ في تحديث البيانات: {str(e)}', 'error')

    return render_template('edit_guarantor.html', guarantor=guarantor)

@app.route('/guarantors/delete/<int:id>')
def delete_guarantor(id):
    """حذف الضامن"""
    try:
        guarantor = Guarantor.query.get_or_404(id)
        db.session.delete(guarantor)
        db.session.commit()
        flash('تم حذف الضامن بنجاح', 'success')
    except Exception as e:
        flash(f'خطأ في حذف الضامن: {str(e)}', 'error')

    return redirect(url_for('guarantors'))

@app.route('/motorcycles')
def motorcycles():
    """صفحة الدراجات النارية"""
    motorcycles = Motorcycle.query.all()
    return render_template('motorcycles.html', motorcycles=motorcycles)

@app.route('/motorcycles/add', methods=['GET', 'POST'])
def add_motorcycle():
    """إضافة دراجة نارية جديدة"""
    if request.method == 'POST':
        try:
            motorcycle = Motorcycle(
                brand=request.form['brand'],
                model=request.form['model'],
                year=int(request.form.get('year', 2024)),
                engine_size=request.form.get('engine_size', ''),
                color=request.form.get('color', '')
            )
            db.session.add(motorcycle)
            db.session.commit()
            flash('تم إضافة الدراجة النارية بنجاح', 'success')
            return redirect(url_for('motorcycles'))
        except Exception as e:
            flash(f'خطأ في إضافة الدراجة النارية: {str(e)}', 'error')

    return render_template('add_motorcycle.html')

@app.route('/motorcycles/edit/<int:id>', methods=['GET', 'POST'])
def edit_motorcycle(id):
    """تعديل بيانات الدراجة النارية"""
    motorcycle = Motorcycle.query.get_or_404(id)

    if request.method == 'POST':
        try:
            motorcycle.brand = request.form['brand']
            motorcycle.model = request.form['model']
            motorcycle.year = int(request.form.get('year', 2024))
            motorcycle.engine_size = request.form.get('engine_size', '')
            motorcycle.color = request.form.get('color', '')

            db.session.commit()
            flash('تم تحديث بيانات الدراجة النارية بنجاح', 'success')
            return redirect(url_for('motorcycles'))
        except Exception as e:
            flash(f'خطأ في تحديث البيانات: {str(e)}', 'error')

    return render_template('edit_motorcycle.html', motorcycle=motorcycle)

@app.route('/motorcycles/delete/<int:id>')
def delete_motorcycle(id):
    """حذف الدراجة النارية"""
    try:
        motorcycle = Motorcycle.query.get_or_404(id)
        db.session.delete(motorcycle)
        db.session.commit()
        flash('تم حذف الدراجة النارية بنجاح', 'success')
    except Exception as e:
        flash(f'خطأ في حذف الدراجة النارية: {str(e)}', 'error')

    return redirect(url_for('motorcycles'))

@app.route('/invoices')
def invoices():
    """صفحة الفواتير"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    
    query = Invoice.query.join(Customer).join(Motorcycle)
    if search:
        query = query.filter(or_(
            Invoice.invoice_number.contains(search),
            Customer.name.contains(search),
            Motorcycle.brand.contains(search)
        ))
    
    invoices = query.paginate(
        page=page, per_page=10, error_out=False
    )
    
    return render_template('invoices.html', invoices=invoices, search=search)

@app.route('/invoices/add', methods=['GET', 'POST'])
def add_invoice():
    """إضافة فاتورة جديدة"""
    if request.method == 'POST':
        try:
            # إنشاء رقم فاتورة تلقائي
            last_invoice = Invoice.query.order_by(Invoice.id.desc()).first()
            invoice_number = f"INV-{datetime.now().year}{str((last_invoice.id if last_invoice else 0) + 1).zfill(4)}"
            
            total_amount = float(request.form['total_amount'])
            down_payment = float(request.form.get('down_payment', 0))
            installments_count = int(request.form['installments_count'])
            
            remaining_amount = total_amount - down_payment
            installment_amount = remaining_amount / installments_count if installments_count > 0 else 0
            
            invoice = Invoice(
                invoice_number=invoice_number,
                customer_id=int(request.form['customer_id']),
                guarantor_id=int(request.form['guarantor_id']),
                motorcycle_id=int(request.form['motorcycle_id']),
                total_amount=total_amount,
                down_payment=down_payment,
                remaining_amount=remaining_amount,
                installment_amount=installment_amount,
                installments_count=installments_count,
                first_installment_date=datetime.strptime(request.form['first_installment_date'], '%Y-%m-%d').date(),
                currency_id=int(request.form.get('currency_id', 1)),
                notes=request.form.get('notes', '')
            )
            
            db.session.add(invoice)
            db.session.commit()
            
            # إنشاء الأقساط
            create_installments(invoice)
            
            flash('تم إنشاء الفاتورة بنجاح', 'success')
            return redirect(url_for('invoices'))
        except Exception as e:
            flash(f'خطأ في إنشاء الفاتورة: {str(e)}', 'error')
    
    customers = Customer.query.all()
    guarantors = Guarantor.query.all()
    motorcycles = Motorcycle.query.all()
    currencies = Currency.query.filter_by(is_active=True).all()

    return render_template('add_invoice.html',
                         customers=customers,
                         guarantors=guarantors,
                         motorcycles=motorcycles,
                         currencies=currencies)

def create_installments(invoice):
    """إنشاء الأقساط للفاتورة"""
    for i in range(invoice.installments_count):
        due_date = invoice.first_installment_date + timedelta(days=30*i)
        
        installment = Installment(
            invoice_id=invoice.id,
            installment_number=i+1,
            amount=invoice.installment_amount,
            due_date=due_date
        )
        
        db.session.add(installment)
    
    db.session.commit()

@app.route('/receipts')
def receipts():
    """صفحة سندات القبض"""
    page = request.args.get('page', 1, type=int)
    receipts = Receipt.query.join(Customer).paginate(
        page=page, per_page=10, error_out=False
    )

    return render_template('receipts.html', receipts=receipts)

@app.route('/receipts/add', methods=['POST'])
def add_receipt():
    """إضافة سند قبض جديد"""
    try:
        # إنشاء رقم سند تلقائي
        last_receipt = Receipt.query.order_by(Receipt.id.desc()).first()
        receipt_number = f"REC-{datetime.now().year}{str((last_receipt.id if last_receipt else 0) + 1).zfill(4)}"

        receipt = Receipt(
            receipt_number=receipt_number,
            customer_id=int(request.form['customer_id']),
            amount=float(request.form['amount']),
            payment_method=request.form.get('payment_method', 'نقدي'),
            receipt_date=datetime.strptime(request.form['receipt_date'], '%Y-%m-%d').date(),
            currency_id=int(request.form.get('currency_id', 1)),
            notes=request.form.get('notes', '')
        )

        db.session.add(receipt)
        db.session.commit()
        flash('تم إضافة سند القبض بنجاح', 'success')
    except Exception as e:
        flash(f'خطأ في إضافة سند القبض: {str(e)}', 'error')

    return redirect(url_for('receipts'))

@app.route('/receipts/delete/<int:id>')
def delete_receipt(id):
    """حذف سند القبض"""
    try:
        receipt = Receipt.query.get_or_404(id)
        db.session.delete(receipt)
        db.session.commit()
        flash('تم حذف سند القبض بنجاح', 'success')
    except Exception as e:
        flash(f'خطأ في حذف سند القبض: {str(e)}', 'error')

    return redirect(url_for('receipts'))

@app.route('/reports')
def reports():
    """صفحة التقارير"""
    return render_template('reports.html')

@app.route('/api/sales_chart')
def sales_chart():
    """بيانات الرسم البياني للمبيعات"""
    # مبيعات آخر 6 أشهر
    months_data = []
    for i in range(6):
        month_start = date.today().replace(day=1) - timedelta(days=30*i)
        month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)

        sales = db.session.query(func.sum(Invoice.total_amount)).filter(
            Invoice.invoice_date.between(month_start, month_end)
        ).scalar() or 0

        months_data.append({
            'month': month_start.strftime('%Y-%m'),
            'sales': float(sales)
        })

    return jsonify(months_data[::-1])  # عكس الترتيب لإظهار الأحدث أولاً

@app.route('/api/customers')
def api_customers():
    """API للحصول على قائمة العملاء"""
    customers = Customer.query.all()
    customers_data = []
    for customer in customers:
        customers_data.append({
            'id': customer.id,
            'name': customer.name,
            'phone': customer.phone,
            'debt': customer.get_total_debt()
        })
    return jsonify(customers_data)

@app.route('/invoices/view/<int:id>')
def view_invoice(id):
    """عرض تفاصيل الفاتورة"""
    invoice = Invoice.query.get_or_404(id)

    # تحديث حالة الأقساط المتأخرة
    update_overdue_installments(invoice)

    return render_template('view_invoice.html', invoice=invoice)

def update_overdue_installments(invoice):
    """تحديث حالة الأقساط المتأخرة"""
    today = date.today()
    for installment in invoice.installments:
        if not installment.is_paid and installment.due_date < today:
            installment.is_overdue = True

    db.session.commit()

@app.route('/invoices/edit/<int:id>', methods=['GET', 'POST'])
def edit_invoice(id):
    """تعديل الفاتورة"""
    invoice = Invoice.query.get_or_404(id)

    if request.method == 'POST':
        try:
            # تحديث البيانات الأساسية
            invoice.customer_id = int(request.form['customer_id'])
            invoice.guarantor_id = int(request.form['guarantor_id'])
            invoice.motorcycle_id = int(request.form['motorcycle_id'])

            # تحديث المبالغ
            old_total = invoice.total_amount
            new_total = float(request.form['total_amount'])
            new_down_payment = float(request.form.get('down_payment', 0))
            new_installments_count = int(request.form['installments_count'])

            invoice.total_amount = new_total
            invoice.down_payment = new_down_payment
            invoice.remaining_amount = new_total - new_down_payment
            invoice.installments_count = new_installments_count
            invoice.installment_amount = invoice.remaining_amount / new_installments_count if new_installments_count > 0 else 0
            invoice.first_installment_date = datetime.strptime(request.form['first_installment_date'], '%Y-%m-%d').date()
            invoice.currency_id = int(request.form.get('currency_id', 1))
            invoice.notes = request.form.get('notes', '')

            # إذا تغير عدد الأقساط أو المبالغ، نحدث الأقساط
            if (new_total != old_total or
                new_installments_count != len(invoice.installments) or
                new_down_payment != invoice.down_payment):

                # حذف الأقساط القديمة غير المدفوعة
                for installment in invoice.installments:
                    if not installment.is_paid:
                        db.session.delete(installment)

                # إنشاء أقساط جديدة
                paid_installments = len([inst for inst in invoice.installments if inst.is_paid])
                remaining_installments = new_installments_count - paid_installments

                for i in range(remaining_installments):
                    installment_date = invoice.first_installment_date + timedelta(days=30*(paid_installments + i))

                    installment = Installment(
                        invoice_id=invoice.id,
                        installment_number=paid_installments + i + 1,
                        amount=invoice.installment_amount,
                        due_date=installment_date
                    )
                    db.session.add(installment)

            db.session.commit()
            flash('تم تحديث الفاتورة بنجاح', 'success')
            return redirect(url_for('view_invoice', id=invoice.id))

        except Exception as e:
            flash(f'خطأ في تحديث الفاتورة: {str(e)}', 'error')

    customers = Customer.query.all()
    guarantors = Guarantor.query.all()
    motorcycles = Motorcycle.query.all()
    currencies = Currency.query.filter_by(is_active=True).all()

    return render_template('edit_invoice.html',
                         invoice=invoice,
                         customers=customers,
                         guarantors=guarantors,
                         motorcycles=motorcycles,
                         currencies=currencies)

@app.route('/invoices/delete/<int:id>')
def delete_invoice(id):
    """حذف الفاتورة"""
    try:
        invoice = Invoice.query.get_or_404(id)
        db.session.delete(invoice)
        db.session.commit()
        flash('تم حذف الفاتورة بنجاح', 'success')
    except Exception as e:
        flash(f'خطأ في حذف الفاتورة: {str(e)}', 'error')

    return redirect(url_for('invoices'))

@app.route('/installments/pay/<int:id>', methods=['POST'])
def pay_installment(id):
    """تسديد قسط"""
    try:
        installment = Installment.query.get_or_404(id)

        if not installment.is_paid:
            installment.is_paid = True
            installment.paid_amount = installment.amount
            installment.payment_date = date.today()
            installment.is_overdue = False

            # إنشاء سند قبض تلقائي
            last_receipt = Receipt.query.order_by(Receipt.id.desc()).first()
            receipt_number = f"REC-{datetime.now().year}{str((last_receipt.id if last_receipt else 0) + 1).zfill(4)}"

            receipt = Receipt(
                receipt_number=receipt_number,
                customer_id=installment.invoice.customer_id,
                invoice_id=installment.invoice_id,
                installment_id=installment.id,
                amount=installment.amount,
                payment_method='نقدي',
                receipt_date=date.today(),
                notes=f'سداد القسط رقم {installment.installment_number} للفاتورة {installment.invoice.invoice_number}'
            )

            db.session.add(receipt)
            db.session.commit()

            flash(f'تم تسديد القسط رقم {installment.installment_number} بنجاح وإنشاء سند القبض {receipt_number}', 'success')
        else:
            flash('هذا القسط مدفوع مسبقاً', 'warning')

    except Exception as e:
        flash(f'خطأ في تسديد القسط: {str(e)}', 'error')

    return redirect(url_for('view_invoice', id=installment.invoice_id))

# ==================== إدارة العملات ====================

@app.route('/currencies')
def currencies():
    """صفحة إدارة العملات"""
    currencies = Currency.query.all()
    return render_template('currencies.html', currencies=currencies)

@app.route('/currencies/add', methods=['GET', 'POST'])
def add_currency():
    """إضافة عملة جديدة"""
    if request.method == 'POST':
        try:
            # التحقق من عدم وجود عملة أساسية أخرى إذا كانت هذه أساسية
            is_base = request.form.get('is_base') == 'on'
            if is_base:
                # إلغاء العملة الأساسية السابقة
                Currency.query.filter_by(is_base=True).update({'is_base': False})

            currency = Currency(
                name=request.form['name'],
                code=request.form['code'].upper(),
                symbol=request.form['symbol'],
                exchange_rate=float(request.form['exchange_rate']),
                is_base=is_base,
                is_active=request.form.get('is_active') == 'on'
            )

            db.session.add(currency)
            db.session.commit()
            flash('تم إضافة العملة بنجاح', 'success')
            return redirect(url_for('currencies'))
        except Exception as e:
            flash(f'خطأ في إضافة العملة: {str(e)}', 'error')

    return render_template('add_currency.html')

@app.route('/currencies/edit/<int:id>', methods=['GET', 'POST'])
def edit_currency(id):
    """تعديل العملة"""
    currency = Currency.query.get_or_404(id)

    if request.method == 'POST':
        try:
            # التحقق من عدم وجود عملة أساسية أخرى إذا كانت هذه أساسية
            is_base = request.form.get('is_base') == 'on'
            if is_base and not currency.is_base:
                # إلغاء العملة الأساسية السابقة
                Currency.query.filter_by(is_base=True).update({'is_base': False})

            currency.name = request.form['name']
            currency.code = request.form['code'].upper()
            currency.symbol = request.form['symbol']
            currency.exchange_rate = float(request.form['exchange_rate'])
            currency.is_base = is_base
            currency.is_active = request.form.get('is_active') == 'on'

            db.session.commit()
            flash('تم تحديث العملة بنجاح', 'success')
            return redirect(url_for('currencies'))
        except Exception as e:
            flash(f'خطأ في تحديث العملة: {str(e)}', 'error')

    return render_template('edit_currency.html', currency=currency)

@app.route('/currencies/delete/<int:id>')
def delete_currency(id):
    """حذف العملة"""
    try:
        currency = Currency.query.get_or_404(id)

        # التحقق من عدم وجود بيانات مرتبطة
        if currency.motorcycles or currency.invoices or currency.receipts:
            flash('لا يمكن حذف هذه العملة لوجود بيانات مرتبطة بها', 'error')
        elif currency.is_base:
            flash('لا يمكن حذف العملة الأساسية', 'error')
        else:
            db.session.delete(currency)
            db.session.commit()
            flash('تم حذف العملة بنجاح', 'success')
    except Exception as e:
        flash(f'خطأ في حذف العملة: {str(e)}', 'error')

    return redirect(url_for('currencies'))

@app.route('/api/currencies')
def api_currencies():
    """API للحصول على قائمة العملات النشطة"""
    currencies = Currency.query.filter_by(is_active=True).all()
    currencies_data = []
    for currency in currencies:
        currencies_data.append({
            'id': currency.id,
            'name': currency.name,
            'code': currency.code,
            'symbol': currency.symbol,
            'exchange_rate': currency.exchange_rate,
            'is_base': currency.is_base
        })
    return jsonify(currencies_data)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
